# Telegram发送开关操作指南

## 🎯 快速操作

### 关闭Telegram发送
```python
# 在 pythia_integrated_complete.py 第27行
ENABLE_TELEGRAM_SEND = False  # 设置为False关闭
```

### 开启Telegram发送
```python
# 在 pythia_integrated_complete.py 第27行
ENABLE_TELEGRAM_SEND = True   # 设置为True开启
```

## 📍 修改位置

文件: `pythia_integrated_complete.py`
行号: 第27行左右

```python
# 🔧 Telegram发送开关 - 设置为False关闭发送功能
ENABLE_TELEGRAM_SEND = False  # 改为True开启，False关闭
```

## 🔍 影响的功能

### 1. 报告生成时的自动发送
- 位置: `save_report_to_file` 方法
- 效果: 关闭后不会自动发送图片到群组

### 2. 监控模式的自动发送
- 位置: `monitor_html_files` 函数
- 效果: 关闭后监控到新文件也不会发送

## 💬 运行时的提示信息

### 关闭状态
```
🔇 Telegram发送已关闭
```

### 开启状态
```
✅ 图片已成功发送到Telegram群组
```

## 🔄 无需重启

修改开关后无需重启程序，下次运行时自动生效。

## 🛡️ 安全性

- 即使开关开启，如果没有配置bot_token和chat_id，也不会发送
- 开关只是额外的控制层，不影响其他功能
- 关闭发送不影响图片生成和本地保存

## 📝 使用建议

1. **测试阶段**: 建议关闭，避免频繁发送测试消息
2. **生产环境**: 根据需要开启或关闭
3. **临时关闭**: 不需要修改配置文件，只需改这一个开关
