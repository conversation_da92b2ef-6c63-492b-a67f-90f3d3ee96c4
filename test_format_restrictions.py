#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Bot格式限制功能
验证系统提示词是否正确禁止了格式化语法
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_format_restrictions():
    """测试格式限制功能"""
    print("🚫 测试Bot格式限制功能")
    print("=" * 60)
    
    try:
        from bot import AIBot
        
        # 创建bot实例
        bot = AIBot()
        print("✅ Bot实例创建成功")
        
        # 获取系统提示词
        print("\n📋 检查中文系统提示词...")
        chinese_prompt = bot._get_chinese_system_prompt()
        
        print("\n📋 检查英文系统提示词...")
        english_prompt = bot._get_english_system_prompt()
        
        # 检查格式限制规则
        format_keywords = [
            "格式要求",
            "Format Requirements",
            "纯文本格式",
            "plain text format",
            "禁止",
            "forbidden",
            "**粗体**",
            "**bold**",
            "*斜体*",
            "*italic*",
            "列表",
            "lists",
            "Markdown",
            "HTML"
        ]
        
        print(f"\n🔍 检查中文提示词格式限制:")
        chinese_has_restrictions = False
        for keyword in format_keywords:
            if keyword in chinese_prompt:
                print(f"   ✅ 包含: {keyword}")
                chinese_has_restrictions = True
        
        print(f"\n🔍 检查英文提示词格式限制:")
        english_has_restrictions = False
        for keyword in format_keywords:
            if keyword in english_prompt:
                print(f"   ✅ 包含: {keyword}")
                english_has_restrictions = True
        
        # 显示格式限制部分
        print(f"\n📄 中文提示词中的格式限制部分:")
        chinese_lines = chinese_prompt.split('\n')
        in_format_section = False
        format_lines = []
        
        for line in chinese_lines:
            if "格式要求" in line:
                in_format_section = True
            elif in_format_section and line.strip() == "":
                if format_lines and not any(keyword in line for keyword in ["禁止", "绝对", "严禁"]):
                    break
            
            if in_format_section:
                format_lines.append(line)
                if len(format_lines) >= 15:  # 限制显示行数
                    break
        
        for i, line in enumerate(format_lines, 1):
            print(f"   {i:2d}: {line}")
        
        # 检查关键禁止项
        prohibited_items = {
            "粗体语法": "**粗体**" in chinese_prompt,
            "斜体语法": "*斜体*" in chinese_prompt,
            "代码语法": "`代码`" in chinese_prompt,
            "列表语法": "- 列表" in chinese_prompt,
            "标题语法": "# 标题" in chinese_prompt,
            "引用语法": "> 引用" in chinese_prompt,
            "代码块语法": "```代码块```" in chinese_prompt
        }
        
        print(f"\n🚫 格式禁止项检查:")
        all_prohibited = True
        for item, mentioned in prohibited_items.items():
            status = "✅ 已禁止" if mentioned else "❌ 未提及"
            print(f"   {status} {item}")
            if not mentioned:
                all_prohibited = False
        
        # 统计信息
        print(f"\n📊 提示词统计:")
        print(f"   中文提示词长度: {len(chinese_prompt)} 字符")
        print(f"   英文提示词长度: {len(english_prompt)} 字符")
        print(f"   中文提示词行数: {chinese_prompt.count(chr(10))} 行")
        print(f"   英文提示词行数: {english_prompt.count(chr(10))} 行")
        
        # 总结
        print(f"\n📋 检查结果:")
        print(f"   中文格式限制: {'✅ 已配置' if chinese_has_restrictions else '❌ 未配置'}")
        print(f"   英文格式限制: {'✅ 已配置' if english_has_restrictions else '❌ 未配置'}")
        print(f"   禁止项完整性: {'✅ 完整' if all_prohibited else '❌ 不完整'}")
        
        return chinese_has_restrictions and english_has_restrictions and all_prohibited
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_format_examples():
    """显示格式示例"""
    print(f"\n📝 格式对比示例:")
    print(f"=" * 60)
    
    print(f"❌ 错误格式 (禁止使用):")
    print(f"   **24小时交易量**：过去一天交易量达到了**622万多美元**！")
    print(f"   *流动性*：目前有*1275万多美元*的流动性")
    print(f"   - 交易对总数: 2个")
    print(f"   - 市值: $92,588,346")
    print(f"   > 市场情绪显示悲观")
    print(f"   # 重要数据")
    print(f"   `PYTHIA`代币价格")
    
    print(f"\n✅ 正确格式 (应该使用):")
    print(f"   24小时交易量：过去一天交易量达到了622万多美元！")
    print(f"   流动性：目前有1275万多美元的流动性")
    print(f"   交易对总数有2个，市值是92,588,346美元")
    print(f"   市场情绪显示悲观，不过别担心~ 🐭")
    print(f"   重要数据显示PYTHIA代币价格稳定")
    
    print(f"\n💡 格式要求总结:")
    print(f"   ✅ 可以使用: 普通文字、emoji、标点符号")
    print(f"   ❌ 禁止使用: 任何Markdown、HTML格式语法")
    print(f"   🎯 目标效果: 像微信聊天一样的纯文本对话")

def create_format_reminder():
    """创建格式提醒文档"""
    reminder_content = """# Bot格式限制提醒

## 🚫 严格禁止的格式

### Markdown格式
- **粗体** 和 *斜体*
- `代码` 和 ```代码块```
- # 标题 和 ## 子标题
- [链接](url) 格式
- > 引用格式
- - 列表 和 * 列表 和 1. 编号列表

### HTML格式
- <b>粗体</b> 和 <i>斜体</i>
- <code>代码</code>
- 任何HTML标签

## ✅ 允许使用的格式

- 普通文字
- emoji表情符号 🐭 📊 💡
- 标点符号：。，！？：；""''
- 数字和字母
- 空格和换行

## 🎯 目标效果

让Bot的回答就像普通的微信聊天一样，纯文本对话，自然流畅。

## 📝 示例对比

❌ 错误：**PYTHIA代币**的*市值*是`$92,588,346`
✅ 正确：PYTHIA代币的市值是92,588,346美元

❌ 错误：
- 交易量：622万美元
- 流动性：1275万美元

✅ 正确：交易量是622万美元，流动性有1275万美元

## 🔧 技术实现

在bot.py的系统提示词中已经添加了严格的格式限制规则，确保AI不会使用任何格式化语法。
"""
    
    try:
        with open("Bot格式限制提醒.md", "w", encoding="utf-8") as f:
            f.write(reminder_content)
        print(f"\n📄 已创建格式提醒文档: Bot格式限制提醒.md")
        return True
    except Exception as e:
        print(f"❌ 创建文档失败: {e}")
        return False

if __name__ == "__main__":
    print(f"🕒 测试开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试格式限制
    success = test_format_restrictions()
    
    # 显示格式示例
    show_format_examples()
    
    # 创建提醒文档
    doc_created = create_format_reminder()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   格式限制配置: {'✅ 成功' if success else '❌ 失败'}")
    print(f"   提醒文档创建: {'✅ 成功' if doc_created else '❌ 失败'}")
    
    if success:
        print("\n🎉 格式限制配置成功！")
        print("🤖 Bot现在会严格使用纯文本格式回答")
        print("🚫 不会再出现**粗体**、*斜体*、- 列表等格式")
        print("💬 回答效果就像普通微信聊天一样自然")
    else:
        print("\n❌ 格式限制配置失败，请检查系统提示词")
    
    print(f"\n🕒 测试结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
