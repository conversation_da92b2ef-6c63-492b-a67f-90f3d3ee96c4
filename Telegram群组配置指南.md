# Telegram群组配置指南

## 🎯 当前配置

### 群组ID: -2681225020

## 📍 配置位置

### 文件: config.py

#### 1. 报告发送配置 (第165-169行)
```python
# Telegram配置
"telegram": {
    "bot_token": "**********************************************",
    "chat_id": "-2681225020"  # 新的群组ID
}
```

#### 2. 群组列表配置 (第18-24行)
```python
# Telegram群组/频道配置（用于发送报告）
TELEGRAM_REPORT_CHATS = [
    "-2681225020",   # 新的PYTHIA群组ID
    # 可以添加更多群组/频道/用户ID
]
```

## 🔧 功能说明

### 1. 报告自动发送
- 使用 `OUTPUT_CONFIG["telegram"]["chat_id"]`
- 当生成新报告时自动发送到指定群组
- 需要 `ENABLE_TELEGRAM_SEND = True`

### 2. AI Bot交互
- Bot可以在任何群组中工作
- 支持私聊和群组聊天
- 群组中需要@机器人才会回应

### 3. 订阅功能
- 用户可以订阅报告推送
- 支持多个群组同时订阅
- 使用 `TELEGRAM_REPORT_CHATS` 列表

## 🎮 使用方法

### 在群组中使用Bot
```
@pythia_is_ai_bot 你好
@pythia_is_ai_bot PYTHIA代币价格多少？
```

### 私聊使用Bot
```
你好
PYTHIA代币价格多少？
```

## 🔄 配置修改

如需更换群组，修改以下两处：
1. `OUTPUT_CONFIG["telegram"]["chat_id"]` - 报告发送
2. `TELEGRAM_REPORT_CHATS` - 群组列表

## ⚠️ 注意事项

1. 群组ID必须是负数（群组）或正数（用户）
2. Bot必须被添加到群组并有发送消息权限
3. 配置修改后立即生效，无需重启
