#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动群组检测功能
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_auto_group_configuration():
    """测试自动群组配置"""
    print("🤖 测试自动群组检测功能")
    print("=" * 60)
    
    try:
        # 导入配置
        from config import OUTPUT_CONFIG
        
        # 检查自动群组配置
        print("📊 自动群组配置检查:")
        telegram_config = OUTPUT_CONFIG.get("telegram", {})
        
        bot_token = telegram_config.get("bot_token")
        chat_id = telegram_config.get("chat_id")
        auto_group_mode = telegram_config.get("auto_group_mode", False)
        fallback_chat_id = telegram_config.get("fallback_chat_id")
        
        print(f"   Bot Token: {bot_token[:20]}...{bot_token[-10:] if bot_token else 'None'}")
        print(f"   Chat ID: {chat_id}")
        print(f"   自动群组模式: {auto_group_mode}")
        print(f"   备用群组ID: {fallback_chat_id}")
        
        # 验证配置
        config_status = "✅ 配置正确" if auto_group_mode and chat_id == "AUTO_DETECT" else "❌ 配置需要调整"
        print(f"   配置状态: {config_status}")
        
        # 检查活跃群组文件
        print(f"\n📱 活跃群组文件检查:")
        active_groups_file = "data/active_groups.json"
        
        if os.path.exists(active_groups_file):
            with open(active_groups_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                groups = data.get('groups', [])
                group_info = data.get('group_info', {})
                last_updated = data.get('last_updated', '未知')
                
                print(f"   活跃群组文件: 存在")
                print(f"   群组数量: {len(groups)}")
                print(f"   最后更新: {last_updated}")
                
                if groups:
                    print(f"   群组列表:")
                    for group_id in groups:
                        info = group_info.get(group_id, {})
                        name = info.get('name', '未知群组')
                        last_active = info.get('last_active', '未知')
                        print(f"     - {group_id}: {name} (最后活跃: {last_active})")
                else:
                    print(f"   📝 暂无活跃群组记录")
        else:
            print(f"   活跃群组文件: 不存在（将在Bot首次使用时创建）")
        
        # 测试PythiaIntegratedAnalyzer配置
        print(f"\n📈 报告生成器配置测试:")
        try:
            from pythia_integrated_complete import PythiaIntegratedAnalyzer
            analyzer = PythiaIntegratedAnalyzer()
            
            print(f"   ✅ 分析器实例创建成功")
            print(f"   自动群组模式: {analyzer.auto_group_mode}")
            print(f"   备用群组ID: {analyzer.fallback_chat_id}")
            print(f"   活跃群组数量: {len(analyzer.active_groups)}")
            
            # 测试获取目标群组
            target_chat_ids = analyzer.get_target_chat_ids()
            print(f"   目标群组: {target_chat_ids}")
            
        except Exception as e:
            print(f"   ❌ 分析器配置测试失败: {e}")
        
        # 测试Bot配置
        print(f"\n🤖 Bot配置测试:")
        try:
            from bot import AIBot
            bot = AIBot()
            
            print(f"   ✅ Bot实例创建成功")
            print(f"   活跃群组文件: {bot.active_groups_file}")
            
            # 模拟记录群组
            test_chat_id = "-2681225020"
            test_group_name = "测试群组"
            
            print(f"   🧪 模拟记录群组: {test_chat_id}")
            bot._record_active_group(test_chat_id, test_group_name)
            print(f"   ✅ 群组记录测试完成")
            
        except Exception as e:
            print(f"   ❌ Bot配置测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_guide():
    """显示使用指南"""
    print(f"\n📖 自动群组检测使用指南:")
    print(f"=" * 60)
    
    print(f"🎯 工作原理:")
    print(f"   1. Bot在群组中被@时，自动记录群组ID")
    print(f"   2. 报告生成时，自动发送到所有活跃群组")
    print(f"   3. 无需手动配置群组ID")
    
    print(f"\n🔧 配置要求:")
    print(f"   ✅ auto_group_mode: True")
    print(f"   ✅ chat_id: 'AUTO_DETECT'")
    print(f"   ✅ fallback_chat_id: 备用群组ID")
    
    print(f"\n🚀 使用步骤:")
    print(f"   1. 将Bot添加到目标群组")
    print(f"   2. 在群组中@Bot发送任意消息")
    print(f"   3. Bot自动记录该群组为活跃群组")
    print(f"   4. 下次生成报告时自动发送到该群组")
    
    print(f"\n💡 优势:")
    print(f"   🎯 无需手动配置群组ID")
    print(f"   🔄 支持多群组自动发送")
    print(f"   📱 动态添加/移除群组")
    print(f"   🛡️ 有备用群组保障")
    
    print(f"\n📝 注意事项:")
    print(f"   - Bot需要在群组中有发送消息权限")
    print(f"   - 群组中需要@Bot才会被记录")
    print(f"   - 活跃群组信息保存在 data/active_groups.json")

def create_auto_group_guide():
    """创建自动群组指南"""
    guide_content = """# 自动群组检测功能指南

## 🎯 功能概述

自动群组检测功能让Bot能够：
- 自动识别和记录活跃群组
- 无需手动配置群组ID
- 支持多群组同时发送报告
- 动态管理群组列表

## 🔧 配置说明

### config.py 配置
```python
"telegram": {
    "bot_token": "你的Bot Token",
    "chat_id": "AUTO_DETECT",        # 启用自动检测
    "auto_group_mode": True,         # 开启自动群组模式
    "fallback_chat_id": "-2681225020" # 备用群组ID
}
```

## 🚀 使用流程

### 1. 添加Bot到群组
- 将 @pythia_is_ai_bot 添加到目标群组
- 确保Bot有发送消息权限

### 2. 激活群组记录
在群组中发送：
```
@pythia_is_ai_bot 你好
```

### 3. 自动记录
- Bot收到@消息后自动记录群组ID
- 群组信息保存到 data/active_groups.json

### 4. 自动发送
- 生成报告时自动发送到所有活跃群组
- 无需额外配置

## 📱 群组管理

### 查看活跃群组
活跃群组信息保存在：`data/active_groups.json`

```json
{
  "groups": ["-2681225020", "-1001234567890"],
  "last_updated": "2024-01-01T12:00:00",
  "group_info": {
    "-2681225020": {
      "name": "PYTHIA群组",
      "last_active": "2024-01-01T12:00:00"
    }
  }
}
```

### 移除群组
如需移除某个群组，编辑 `data/active_groups.json` 文件，从 `groups` 数组中删除对应ID。

## 🔄 工作模式

### 自动群组模式 (推荐)
```python
auto_group_mode: True
chat_id: "AUTO_DETECT"
```
- 发送到所有活跃群组
- 动态管理群组列表

### 传统模式
```python
auto_group_mode: False
chat_id: "具体群组ID"
```
- 只发送到指定群组
- 需要手动配置

## 🛡️ 备用机制

如果没有活跃群组记录，系统会：
1. 使用 `fallback_chat_id` 作为备用群组
2. 确保报告能够正常发送

## 💡 最佳实践

1. **初次设置**：在主要群组中@Bot激活记录
2. **多群组管理**：在每个目标群组中都@Bot一次
3. **定期检查**：查看 `data/active_groups.json` 确认群组列表
4. **备用配置**：设置 `fallback_chat_id` 作为保障

## 🔍 故障排除

### Bot不记录群组
- 检查Bot是否有群组发送权限
- 确认消息格式：`@pythia_is_ai_bot 消息内容`
- 查看Bot日志确认是否收到消息

### 报告不发送
- 检查 `ENABLE_TELEGRAM_SEND = True`
- 确认 `auto_group_mode = True`
- 验证活跃群组文件是否存在

### 多群组发送失败
- 检查每个群组的Bot权限
- 确认群组ID格式正确
- 查看发送日志定位具体问题

## 📊 监控和日志

系统会记录：
- 群组记录操作
- 发送成功/失败状态
- 活跃群组更新时间

查看日志了解详细运行状态。
"""
    
    try:
        with open("自动群组检测功能指南.md", "w", encoding="utf-8") as f:
            f.write(guide_content)
        print(f"\n📄 已创建功能指南: 自动群组检测功能指南.md")
        return True
    except Exception as e:
        print(f"❌ 创建指南失败: {e}")
        return False

if __name__ == "__main__":
    success = test_auto_group_configuration()
    
    show_usage_guide()
    create_auto_group_guide()
    
    if success:
        print(f"\n🎉 自动群组检测功能配置成功！")
        print(f"🤖 Bot现在可以自动检测和管理群组")
        print(f"📱 在群组中@Bot即可激活自动记录")
        print(f"🚀 报告将自动发送到所有活跃群组")
    else:
        print(f"\n❌ 配置测试失败，请检查配置文件")
