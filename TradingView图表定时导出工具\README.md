# TradingView图表定时导出工具

## 功能特点

- 🕐 **定时任务模式** - 每1分钟自动导出图表
- 📁 **智能存储** - 自动存储到data_Images文件夹
- 🏷️ **时间戳命名** - 文件名包含时间戳，避免覆盖
- 📦 **单文件部署** - 内置HTML模板，无需额外文件
- 🎯 **专注PYTHIAUSD** - 专门监控PYTH代币价格走势

## 文件结构

```
.
├── quick_chart.py          # 主程序（内置HTML模板）
├── requirements.txt        # 依赖包列表
├── README.md              # 说明文档
└── data_Images/           # 图表存储目录（自动创建）
    ├── CRYPTO_PYTHIAUSD_20240724_143022.png
    ├── CRYPTO_PYTHIAUSD_20240724_144022.png
    └── CRYPTO_PYTHIAUSD_20240724_145022.png
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 定时任务模式（推荐）
```bash
python quick_chart.py
# 或
python quick_chart.py schedule
```
- 每1分钟自动导出图表
- 按Ctrl+C停止任务
- 文件自动保存到data_Images/目录

### 2. 一次性导出
```bash
python quick_chart.py once
```
- 立即导出一次，然后退出

### 3. Python代码中使用

```python
from quick_chart import quick_export_chart

# 导出PYTHIAUSD到data_Images目录，自动时间戳命名
quick_export_chart("CRYPTO:PYTHIAUSD")

# 指定文件名（仍保存到data_Images目录）
quick_export_chart("CRYPTO:PYTHIAUSD", "pyth_custom.png")

# 指定完整路径
quick_export_chart("CRYPTO:PYTHIAUSD", "custom_dir/pyth.png")
```

### 4. 当前监控的交易对

程序专门监控：
- **CRYPTO:PYTHIAUSD** - PYTH代币对美元的价格走势

如需修改，可编辑 `quick_chart.py` 中的 `scheduled_export()` 函数中的 `symbol` 变量。

## 特点

- ⚡ **超快速** - 精简代码，最快截图
- 🎯 **专注核心** - 只做截图，不做多余功能
- 📦 **单文件** - 无需配置文件，开箱即用
- 🔧 **零配置** - 默认720x480尺寸，暗色主题

## 注意事项

1. 首次运行自动下载Chrome驱动
2. 需要网络连接加载TradingView
3. 等待时间约15秒