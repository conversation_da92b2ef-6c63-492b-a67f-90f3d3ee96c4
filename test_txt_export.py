#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PYTHIA API数据导出到txt文件功能
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pythia_integrated_complete import PythiaIntegratedAnalyzer
    print("✅ 成功导入PythiaIntegratedAnalyzer")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保config.py文件存在且配置正确")
    sys.exit(1)

def test_txt_export():
    """测试txt导出功能"""
    print("🧪 开始测试PYTHIA API数据导出到txt文件功能")
    print("=" * 60)
    
    try:
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        print("✅ 分析器实例创建成功")
        
        # 测试数据获取
        print("\n📊 获取PYTHIA交易对数据...")
        pairs_data, filter_stats = analyzer.search_all_pythia_pairs()
        
        if not pairs_data:
            print("❌ 无法获取交易对数据，可能是网络问题或API限制")
            return False
            
        print(f"✅ 成功获取 {len(pairs_data)} 个交易对")
        
        # 分析数据
        print("\n🔍 分析数据...")
        formatted_data = {"pairs": pairs_data}
        analysis = analyzer.analyze_price_data(formatted_data)
        sentiment = analyzer.analyze_market_sentiment(pairs_data)
        metrics = analyzer.calculate_comprehensive_metrics(pairs_data, analysis)
        
        print(f"✅ 分析完成:")
        print(f"   - 分析的交易对: {len(analysis)}")
        print(f"   - 市场情绪: {sentiment.get('sentiment', 'unknown')}")
        print(f"   - 总交易量: ${metrics.get('total_volume', 0):,.2f}")
        
        # 测试txt导出功能
        print("\n💾 测试txt文件导出...")
        txt_filepath = analyzer.save_api_data_to_txt(pairs_data, analysis, sentiment, metrics)
        
        if txt_filepath and os.path.exists(txt_filepath):
            print(f"✅ txt文件导出成功: {txt_filepath}")
            
            # 检查文件大小
            file_size = os.path.getsize(txt_filepath)
            print(f"📁 文件大小: {file_size:,} 字节")
            
            # 读取文件前几行验证内容
            print("\n📄 文件内容预览:")
            with open(txt_filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:10]):  # 显示前10行
                    print(f"   {i+1:2d}: {line.rstrip()}")
                if len(lines) > 10:
                    print(f"   ... (还有 {len(lines)-10} 行)")
            
            return True
        else:
            print("❌ txt文件导出失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_report_generation():
    """测试完整报告生成（包含txt导出）"""
    print("\n🚀 测试完整报告生成...")
    print("=" * 60)
    
    try:
        analyzer = PythiaIntegratedAnalyzer()
        
        # 生成完整报告（会自动调用txt导出）
        print("📊 生成专业报告...")
        report_content = analyzer.generate_professional_report()
        
        if report_content and "网络连接异常" not in report_content:
            print("✅ 专业报告生成成功")
            print(f"📄 报告长度: {len(report_content):,} 字符")
            return True
        else:
            print("❌ 专业报告生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整报告生成测试失败: {e}")
        return False

if __name__ == "__main__":
    print(f"🕒 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试1: 单独测试txt导出功能
    success1 = test_txt_export()
    
    # 测试2: 测试完整报告生成
    success2 = test_full_report_generation()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   txt导出功能: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"   完整报告生成: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！新功能已成功添加。")
        print("💡 现在每次生成报告时，API数据都会同时保存到txt文件中。")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和网络连接。")
    
    print(f"\n🕒 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
