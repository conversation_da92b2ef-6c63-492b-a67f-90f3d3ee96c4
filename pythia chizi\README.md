# 池子价值监控警报系统

一个实时监控加密货币流动性池价值变化的警报系统，支持多种警报规则和Telegram通知。

## 功能特性

- ⏱️ **实时监控**: 每秒请求一次池子数据
- 💾 **数据记录**: 自动保存到JSON文件，保留24小时历史数据
- 🚨 **多重警报**: 支持价格波动、流动性变化、交易量异常等多种警报规则
- 📱 **Telegram通知**: 自动发送警报消息到Telegram
- 📊 **实时显示**: 控制台实时显示价格、流动性等关键指标

## 警报规则

系统内置以下警报规则：

1. **1分钟波动超5%** - 检测短期价格剧烈波动
2. **5分钟波动超10%** - 检测中期价格大幅变化  
3. **15分钟波动超20%** - 检测长期价格趋势变化
4. **流动性骤降30%** - 检测流动性大幅撤出
5. **交易量暴增500%** - 检测异常交易活动

## 安装依赖

```bash
pip install requests asyncio
```

## 配置使用

### 1. 获取Telegram Chat ID（可选）

如果要接收Telegram通知：

1. 与 @userinfobot 对话获取你的Chat ID
2. 在 `config.py` 中设置 `TELEGRAM_CONFIG["chat_id"]`

### 2. 修改监控代币

在 `config.py` 中修改 `MONITOR_CONFIG["token_query"]` 为你要监控的代币名称。

### 3. 运行监控

```bash
python pool_monitor_alert.py
```

## 输出示例

```
🚀 开始监控 PYTHIA 池子价值...
📊 数据文件: pool_data.json
🤖 Telegram Bot: 已配置
==================================================
[14:30:15] 💰 PYTHIA
  价格: $0.001234
  流动性: $45,678.90
  24h交易量: $12,345.67
  活跃交易对: 3
  价格变化: 📈 *****%
  流动性变化: 📉 -1.23%
--------------------------------------------------
```

## 警报消息示例

```
🚨 1分钟波动超5%
价格上涨: 7.25%
当前价格: $0.001324
1分钟前: $0.001234
```

## 文件说明

- `pool_monitor_alert.py` - 主监控程序
- `pool_value_fetcher.py` - 池子数据获取器
- `config.py` - 配置文件
- `pool_data.json` - 自动生成的历史数据文件

## 自定义警报规则

你可以在 `config.py` 中的 `ALERT_RULES` 中添加或修改警报规则：

```python
{
    "name": "自定义规则名称",
    "condition": "price_change",  # price_change, liquidity_drop, volume_spike
    "threshold": 15.0,  # 阈值百分比
    "timeframe_minutes": 3,  # 时间窗口（分钟）
    "enabled": True  # 是否启用
}
```

## 注意事项

- 系统会自动处理API限制和网络错误
- 历史数据只保留24小时，超过会自动清理
- 连续10次获取数据失败会暂停监控
- 使用 Ctrl+C 可以安全停止监控

## 技术支持

如有问题或建议，请检查：
1. 网络连接是否正常
2. API是否可访问
3. Telegram Bot Token是否有效
4. Chat ID是否正确设置