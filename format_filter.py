#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式过滤器 - 清理AI回答中的格式化语法
可以作为额外的安全措施，确保输出纯文本
"""

import re

class FormatFilter:
    """格式过滤器类"""
    
    def __init__(self):
        # 定义需要清理的格式模式
        self.patterns = [
            # Markdown粗体和斜体
            (r'\*\*(.*?)\*\*', r'\1'),  # **粗体** -> 粗体
            (r'\*(.*?)\*', r'\1'),      # *斜体* -> 斜体
            
            # Markdown代码
            (r'`(.*?)`', r'\1'),        # `代码` -> 代码
            (r'```.*?```', ''),         # ```代码块``` -> 删除
            
            # Markdown标题
            (r'^#{1,6}\s*(.*?)$', r'\1', re.MULTILINE),  # # 标题 -> 标题
            
            # Markdown链接
            (r'\[(.*?)\]\(.*?\)', r'\1'),  # [文字](链接) -> 文字
            
            # Markdown列表
            (r'^\s*[-*+]\s+', '', re.MULTILINE),  # - 列表项 -> 列表项
            (r'^\s*\d+\.\s+', '', re.MULTILINE),  # 1. 列表项 -> 列表项
            
            # Markdown引用
            (r'^\s*>\s*', '', re.MULTILINE),  # > 引用 -> 引用
            
            # HTML标签
            (r'<b>(.*?)</b>', r'\1'),       # <b>粗体</b> -> 粗体
            (r'<i>(.*?)</i>', r'\1'),       # <i>斜体</i> -> 斜体
            (r'<strong>(.*?)</strong>', r'\1'),  # <strong>粗体</strong> -> 粗体
            (r'<em>(.*?)</em>', r'\1'),     # <em>斜体</em> -> 斜体
            (r'<code>(.*?)</code>', r'\1'), # <code>代码</code> -> 代码
            (r'<.*?>', ''),                 # 其他HTML标签 -> 删除
        ]
    
    def clean_text(self, text: str) -> str:
        """清理文本中的格式化语法"""
        if not text:
            return text
        
        cleaned_text = text
        
        # 应用所有清理模式
        for pattern_info in self.patterns:
            if len(pattern_info) == 2:
                pattern, replacement = pattern_info
                flags = 0
            else:
                pattern, replacement, flags = pattern_info
            
            cleaned_text = re.sub(pattern, replacement, cleaned_text, flags=flags)
        
        # 清理多余的空行
        cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
        
        # 清理行首行尾空白
        lines = cleaned_text.split('\n')
        cleaned_lines = [line.strip() for line in lines]
        cleaned_text = '\n'.join(cleaned_lines)
        
        return cleaned_text.strip()
    
    def validate_text(self, text: str) -> dict:
        """验证文本是否包含格式化语法"""
        if not text:
            return {"is_clean": True, "issues": []}
        
        issues = []
        
        # 检查各种格式问题
        format_checks = [
            (r'\*\*.*?\*\*', "包含粗体语法 **text**"),
            (r'(?<!\*)\*(?!\*).*?(?<!\*)\*(?!\*)', "包含斜体语法 *text*"),
            (r'`.*?`', "包含代码语法 `code`"),
            (r'```.*?```', "包含代码块语法"),
            (r'^#{1,6}\s+', "包含标题语法 # title", re.MULTILINE),
            (r'\[.*?\]\(.*?\)', "包含链接语法 [text](url)"),
            (r'^\s*[-*+]\s+', "包含列表语法 - item", re.MULTILINE),
            (r'^\s*\d+\.\s+', "包含编号列表语法 1. item", re.MULTILINE),
            (r'^\s*>\s+', "包含引用语法 > quote", re.MULTILINE),
            (r'<[^>]+>', "包含HTML标签"),
        ]
        
        for pattern_info in format_checks:
            if len(pattern_info) == 2:
                pattern, description = pattern_info
                flags = 0
            else:
                pattern, description, flags = pattern_info
            
            if re.search(pattern, text, flags=flags):
                issues.append(description)
        
        return {
            "is_clean": len(issues) == 0,
            "issues": issues
        }

def test_format_filter():
    """测试格式过滤器"""
    print("🧹 测试格式过滤器")
    print("=" * 50)
    
    filter = FormatFilter()
    
    # 测试用例
    test_cases = [
        "**24小时交易量**：过去一天 大家交易我的名气 交易量达到了**622万多美元**！好多人在为我贡献呢！",
        "*流动性*：目前有*1275万多美元*的贡献点可以随时换成好吃的！",
        "- 交易对总数: 2个\n- 市值: $92,588,346",
        "> 市场情绪显示悲观",
        "# 重要数据\n## 价格信息",
        "`PYTHIA`代币价格是`$0.092`",
        "```python\nprint('hello')\n```",
        "[PYTHIA官网](https://pythia.com)",
        "<b>粗体文字</b>和<i>斜体文字</i>",
        "1. 第一项\n2. 第二项\n3. 第三项"
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"原文: {test_text}")
        
        # 验证原文
        validation = filter.validate_text(test_text)
        print(f"问题: {', '.join(validation['issues']) if validation['issues'] else '无'}")
        
        # 清理文本
        cleaned = filter.clean_text(test_text)
        print(f"清理后: {cleaned}")
        
        # 验证清理后的文本
        cleaned_validation = filter.validate_text(cleaned)
        print(f"清理效果: {'✅ 成功' if cleaned_validation['is_clean'] else '❌ 仍有问题'}")

if __name__ == "__main__":
    test_format_filter()
