# 文本格式清理替换规则

## 🎯 替换顺序（重要！）

### 第一步：清理列表项
```
查找: ^\s*\*\s+
替换: (空)
说明: 清理行首的 "*   " 格式
```

### 第二步：清理粗体
```
查找: \*\*(.*?)\*\*
替换: $1
说明: 将 **文字** 替换为 文字
```

## 📝 具体替换示例

### 列表项清理
```
原文: *   **24小时交易量**：过去一天...
替换: **24小时交易量**：过去一天...
```

### 粗体清理
```
原文: **24小时交易量**：过去一天...
替换: 24小时交易量：过去一天...
```

### 完整效果
```
原文: *   **24小时交易量**：过去一天...
最终: 24小时交易量：过去一天...
```

## 🔧 在不同编辑器中的使用

### VS Code
1. 按 Ctrl+H 打开替换
2. 点击正则表达式按钮 (.*)
3. 第一次替换：
   - 查找: `^\s*\*\s+`
   - 替换: (留空)
4. 第二次替换：
   - 查找: `\*\*(.*?)\*\*`
   - 替换: `$1`

### Notepad++
1. 按 Ctrl+H 打开替换
2. 选择"正则表达式"模式
3. 第一次替换：
   - 查找: `^\s*\*\s+`
   - 替换: (留空)
4. 第二次替换：
   - 查找: `\*\*(.*?)\*\*`
   - 替换: ``

### Sublime Text
1. 按 Ctrl+H 打开替换
2. 点击正则表达式按钮
3. 使用与VS Code相同的规则

## 🐍 Python脚本使用

```python
import re

def clean_text(text):
    # 第一步：清理列表项
    text = re.sub(r'^\s*\*\s+', '', text, flags=re.MULTILINE)
    # 第二步：清理粗体
    text = re.sub(r'\*\*(.*?)\*\*', r'', text)
    return text

# 使用示例
original = "你的原始文本"
cleaned = clean_text(original)
print(cleaned)
```

## ⚠️ 注意事项

1. **顺序很重要**：必须先清理列表项，再清理粗体
2. **正则表达式**：确保编辑器开启正则表达式模式
3. **备份原文**：替换前建议备份原始文本
4. **逐步替换**：建议分两步进行，便于检查效果

## 🎯 预期效果

清理后的文本应该：
- 没有 `*   ` 开头的列表项
- 没有 `**粗体**` 格式
- 保持原有的内容和emoji
- 变成纯文本对话格式
