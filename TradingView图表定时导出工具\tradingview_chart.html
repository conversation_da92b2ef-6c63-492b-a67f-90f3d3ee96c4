<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Chart for Automation</title>
    <style>
        body, html { 
            margin: 0; 
            padding: 0; 
            overflow: hidden; 
            background-color: #131722;
        }
        #chart_container {
            border: none;
            outline: none;
        }
    </style>
</head>
<body>
    <div id="chart_container" style="width: 720px; height: 480px;"></div>
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        window.addEventListener('load', function() {
            new TradingView.widget({
                "width": 720,
                "height": 480,
                "autosize": false,
                "container_id": "chart_container",
                "symbol": "CRYPTO:PYTHIAUSD",
                "interval": "60",
                "locale": "zh_CN",
                "theme": "dark",
                "hide_top_toolbar": true,
                "hide_side_toolbar": true,
                "hide_legend": true,
                "hide_volume": true,
                "disabled_features": [
                    "use_localstorage_for_settings",
                    "volume_force_overlay",
                    "create_volume_indicator_by_default",
                    "header_widget",
                    "timeframes_toolbar",
                    "edit_buttons_in_legend",
                    "context_menus",
                    "left_toolbar",
                    "control_bar",
                    "border_around_the_chart"
                ],
                "overrides": {
                    "paneProperties.background": "#131722",
                    "paneProperties.vertGridProperties.color": "#363c4e",
                    "paneProperties.horzGridProperties.color": "#363c4e",
                    "paneProperties.rightMargin": 15,
                    "paneProperties.leftMargin": 5,
                    "mainSeriesProperties.candleStyle.upColor": "#26a69a",
                    "mainSeriesProperties.candleStyle.downColor": "#ef5350",
                    "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
                    "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
                    "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350",
                    "scalesProperties.textColor": "#d1d4dc",
                    "scalesProperties.backgroundColor": "#131722"
                },
                "onChartReady": function() {
                    console.log("TradingView chart loaded successfully");
                }
            });
        });
    </script>
</body>
</html>