#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
池子价值监控警报系统 - 单文件版本
每秒监控池子价值变化，记录到JSON文件，并在满足条件时发送Telegram警报
"""

import json
import time
import requests
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import statistics
import os
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams
import io

class PoolMonitorAlert:
    """池子价值监控警报系统"""
    
    def __init__(self):
        # 配置参数 - 在这里修改你的设置
        self.bot_token = "8240000856:AAFyqMzovjOLM3_m9xCD9eqr4g13Kqfl9fU"
        self.chat_id = "6511182257"  # 你的Telegram Chat ID
        self.token_query = "PYTHIA"  # 要监控的代币
        self.data_file = "pool_data.json"
        
        # API配置
        self.base_url = "https://api.dexscreener.com"
        self.timeout = 30
        
        # 警报规则配置
        self.alert_rules = [
            {"name": "1分钟波动超5%", "condition": "price_change", "threshold": 5.0, "timeframe": 1},
            {"name": "5分钟波动超10%", "condition": "price_change", "threshold": 10.0, "timeframe": 5},
            {"name": "15分钟波动超20%", "condition": "price_change", "threshold": 20.0, "timeframe": 15},
            {"name": "流动性骤降30%", "condition": "liquidity_drop", "threshold": 30.0, "timeframe": 5},
            {"name": "交易量暴增500%", "condition": "volume_spike", "threshold": 500.0, "timeframe": 10},
        ]
        
        # 初始化数据存储
        self.historical_data = self._load_historical_data()
        
        # 定时报告配置
        self.last_minute_report = datetime.now().replace(second=0, microsecond=0)
        
        # 图表配置
        self.setup_matplotlib()
        self.last_chart_time = datetime.now().replace(second=0, microsecond=0)  # 每分钟发送图表
    
    def setup_matplotlib(self):
        """设置matplotlib中文显示和样式"""
        plt.style.use('dark_background')
        rcParams['font.size'] = 10
        rcParams['axes.titlesize'] = 12
        rcParams['axes.labelsize'] = 10
        rcParams['xtick.labelsize'] = 8
        rcParams['ytick.labelsize'] = 8
        rcParams['legend.fontsize'] = 9
        
    def _load_historical_data(self) -> List[Dict]:
        """加载历史数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"❌ 加载历史数据失败: {e}")
        return []
    
    def _save_data(self, data: Dict):
        """保存数据到JSON文件"""
        self.historical_data.append(data)
        
        # 只保留最近24小时的数据
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.historical_data = [
            d for d in self.historical_data 
            if datetime.fromisoformat(d['timestamp']) > cutoff_time
        ]
        
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.historical_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
    
    def get_current_pool_data(self) -> Optional[Dict]:
        """获取当前池子数据"""
        try:
            url = f"{self.base_url}/latest/dex/search"
            params = {"q": self.token_query}
            response = requests.get(url, params=params, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
            elif response.status_code == 429:
                print(f"⚠️ 速率限制，等待2秒...")
                time.sleep(2)
                return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 请求错误: {e}")
            return None
        
        if not data or "pairs" not in data:
            return None
        
        total_liquidity = 0
        total_volume_24h = 0
        price_usd = 0
        active_pairs = 0
        
        for pair in data["pairs"]:
            liquidity = pair.get("liquidity", {}).get("usd", 0)
            volume_24h = pair.get("volume", {}).get("h24", 0)
            
            total_liquidity += liquidity
            total_volume_24h += volume_24h
            
            if liquidity > 0:
                active_pairs += 1
                if price_usd == 0:  # 取第一个有效价格
                    price_usd = float(pair.get("priceUsd", 0))
        
        return {
            "timestamp": datetime.now().isoformat(),
            "total_liquidity_usd": total_liquidity,
            "total_volume_24h_usd": total_volume_24h,
            "price_usd": price_usd,
            "active_pairs": active_pairs,
            "token": self.token_query
        }
    
    def _calculate_percentage_change(self, current_value: float, past_value: float) -> float:
        """计算百分比变化"""
        if past_value == 0:
            return 0
        return ((current_value - past_value) / past_value) * 100
    
    def _get_data_in_timeframe(self, minutes: int) -> List[Dict]:
        """获取指定时间范围内的数据"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [
            d for d in self.historical_data 
            if datetime.fromisoformat(d['timestamp']) > cutoff_time
        ]
    
    def check_alert_conditions(self, current_data: Dict) -> List[Tuple[str, str]]:
        """检查警报条件"""
        alerts = []
        
        for rule in self.alert_rules:
            timeframe_data = self._get_data_in_timeframe(rule["timeframe"])
            
            if len(timeframe_data) < 2:
                continue
            
            if rule["condition"] == "price_change":
                # 价格波动检查
                past_price = timeframe_data[0]['price_usd']
                current_price = current_data['price_usd']
                
                if past_price > 0:
                    change_percent = abs(self._calculate_percentage_change(current_price, past_price))
                    
                    if change_percent >= rule["threshold"]:
                        direction = "上涨" if current_price > past_price else "下跌"
                        message = (f"🚨 {rule['name']}\n"
                                 f"价格{direction}: {change_percent:.2f}%\n"
                                 f"当前价格: ${current_price:.6f}\n"
                                 f"{rule['timeframe']}分钟前: ${past_price:.6f}")
                        alerts.append((rule["name"], message))
            
            elif rule["condition"] == "liquidity_drop":
                # 流动性下降检查
                past_liquidity = timeframe_data[0]['total_liquidity_usd']
                current_liquidity = current_data['total_liquidity_usd']
                
                if past_liquidity > 0:
                    change_percent = self._calculate_percentage_change(current_liquidity, past_liquidity)
                    
                    if change_percent <= -rule["threshold"]:
                        message = (f"🚨 {rule['name']}\n"
                                 f"流动性下降: {abs(change_percent):.2f}%\n"
                                 f"当前流动性: ${current_liquidity:,.2f}\n"
                                 f"{rule['timeframe']}分钟前: ${past_liquidity:,.2f}")
                        alerts.append((rule["name"], message))
            
            elif rule["condition"] == "volume_spike":
                # 交易量暴增检查
                recent_volumes = [d['total_volume_24h_usd'] for d in timeframe_data[-10:]]
                if len(recent_volumes) >= 3:
                    avg_volume = statistics.mean(recent_volumes[:-1])
                    current_volume = current_data['total_volume_24h_usd']
                    
                    if avg_volume > 0:
                        change_percent = self._calculate_percentage_change(current_volume, avg_volume)
                        
                        if change_percent >= rule["threshold"]:
                            message = (f"🚨 {rule['name']}\n"
                                     f"交易量暴增: {change_percent:.2f}%\n"
                                     f"当前24h交易量: ${current_volume:,.2f}\n"
                                     f"平均交易量: ${avg_volume:,.2f}")
                            alerts.append((rule["name"], message))
        
        return alerts
    
    def generate_minute_report(self, current_data: Dict) -> str:
        """生成1分钟报告"""
        current_time = datetime.fromisoformat(current_data['timestamp'])
        
        # 获取1分钟前的数据
        minute_data = self._get_data_in_timeframe(1)
        
        report = f"📊 {self.token_query} 1分钟报告\n"
        report += f"⏰ 时间: {current_time.strftime('%H:%M:%S')}\n\n"
        
        # 当前数据
        report += f"💰 当前价格: ${current_data['price_usd']:.6f}\n"
        report += f"💧 流动性: ${current_data['total_liquidity_usd']:,.2f}\n"
        report += f"📈 24h交易量: ${current_data['total_volume_24h_usd']:,.2f}\n"
        report += f"🔗 活跃交易对: {current_data['active_pairs']}\n"
        
        # 1分钟变化
        if len(minute_data) >= 2:
            past_data = minute_data[0]
            
            price_change = self._calculate_percentage_change(
                current_data['price_usd'], past_data['price_usd']
            )
            liquidity_change = self._calculate_percentage_change(
                current_data['total_liquidity_usd'], past_data['total_liquidity_usd']
            )
            volume_change = self._calculate_percentage_change(
                current_data['total_volume_24h_usd'], past_data['total_volume_24h_usd']
            )
            
            report += f"\n📊 1分钟变化:\n"
            
            # 价格变化
            price_emoji = "🟢" if price_change > 0 else "🔴" if price_change < 0 else "⚪"
            report += f"{price_emoji} 价格: {price_change:+.2f}%\n"
            
            # 流动性变化
            liquidity_emoji = "🟢" if liquidity_change > 0 else "🔴" if liquidity_change < 0 else "⚪"
            report += f"{liquidity_emoji} 流动性: {liquidity_change:+.2f}%\n"
            
            # 交易量变化
            volume_emoji = "🟢" if volume_change > 0 else "🔴" if volume_change < 0 else "⚪"
            report += f"{volume_emoji} 交易量: {volume_change:+.2f}%\n"
            
            # 最高最低价格
            minute_prices = [d['price_usd'] for d in minute_data if d['price_usd'] > 0]
            if minute_prices:
                max_price = max(minute_prices)
                min_price = min(minute_prices)
                report += f"\n📈 1分钟最高: ${max_price:.6f}\n"
                report += f"📉 1分钟最低: ${min_price:.6f}\n"
                
                if max_price != min_price:
                    volatility = ((max_price - min_price) / min_price) * 100
                    report += f"⚡ 波动幅度: {volatility:.2f}%\n"
        
        return report
    
    async def send_telegram_alert(self, message: str):
        """发送Telegram警报"""
        if not self.chat_id:
            print(f"📢 警报消息: {message}")
            return
            
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            payload = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": "HTML"
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ 消息已发送到Telegram")
            else:
                print(f"❌ 发送消息失败: {response.status_code}")
                print(f"📢 消息内容: {message}")
                
        except Exception as e:
            print(f"❌ 发送Telegram消息失败: {e}")
            print(f"📢 消息内容: {message}")
    
    def should_send_minute_report(self) -> bool:
        """检查是否应该发送1分钟报告"""
        current_time = datetime.now().replace(second=0, microsecond=0)
        
        if current_time > self.last_minute_report:
            self.last_minute_report = current_time
            return True
        return False
    
    def should_send_chart(self) -> bool:
        """检查是否应该发送图表（每分钟）"""
        current_time = datetime.now().replace(second=0, microsecond=0)
        
        if current_time > self.last_chart_time:
            self.last_chart_time = current_time
            return True
        return False
    
    def generate_liquidity_chart(self, timeframe_hours: int = 6) -> Optional[io.BytesIO]:
        """生成流动性数据曲线图"""
        try:
            # 获取指定时间范围的数据
            cutoff_time = datetime.now() - timedelta(hours=timeframe_hours)
            chart_data = [
                d for d in self.historical_data 
                if datetime.fromisoformat(d['timestamp']) > cutoff_time and d['total_liquidity_usd'] > 0
            ]
            
            if len(chart_data) < 10:
                print("⚠️ 数据不足，无法生成图表")
                return None
            
            # 准备数据
            timestamps = [datetime.fromisoformat(d['timestamp']) for d in chart_data]
            liquidity_values = [d['total_liquidity_usd'] for d in chart_data]
            price_values = [d['price_usd'] for d in chart_data]
            volume_values = [d['total_volume_24h_usd'] for d in chart_data]
            
            # 创建图表
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
            fig.patch.set_facecolor('#1e1e1e')
            
            # 流动性图表
            ax1.plot(timestamps, liquidity_values, color='#00ff88', linewidth=2, label='流动性')
            ax1.fill_between(timestamps, liquidity_values, alpha=0.3, color='#00ff88')
            ax1.set_title(f'{self.token_query} 流动性变化 ({timeframe_hours}小时)', color='white', fontsize=14)
            ax1.set_ylabel('流动性 (USD)', color='white')
            ax1.tick_params(colors='white')
            ax1.grid(True, alpha=0.3)
            ax1.legend()
            
            # 格式化流动性数值
            ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            # 价格图表
            ax2.plot(timestamps, price_values, color='#ff6b6b', linewidth=2, label='价格')
            ax2.fill_between(timestamps, price_values, alpha=0.3, color='#ff6b6b')
            ax2.set_title(f'{self.token_query} 价格变化', color='white', fontsize=14)
            ax2.set_ylabel('价格 (USD)', color='white')
            ax2.tick_params(colors='white')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            
            # 格式化价格数值
            ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:.6f}'))
            
            # 交易量图表
            ax3.bar(timestamps, volume_values, color='#4ecdc4', alpha=0.7, label='24h交易量')
            ax3.set_title(f'{self.token_query} 24h交易量', color='white', fontsize=14)
            ax3.set_ylabel('交易量 (USD)', color='white')
            ax3.set_xlabel('时间', color='white')
            ax3.tick_params(colors='white')
            ax3.grid(True, alpha=0.3)
            ax3.legend()
            
            # 格式化交易量数值
            ax3.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
            
            # 设置时间轴格式
            for ax in [ax1, ax2, ax3]:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=1))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存到内存
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight', 
                       facecolor='#1e1e1e', edgecolor='none')
            img_buffer.seek(0)
            plt.close()
            
            return img_buffer
            
        except Exception as e:
            print(f"❌ 生成图表失败: {e}")
            plt.close('all')  # 确保清理资源
            return None
    
    async def send_telegram_photo(self, photo_buffer: io.BytesIO, caption: str = ""):
        """发送图片到Telegram"""
        if not self.chat_id:
            print(f"📊 图表已生成但未配置Telegram")
            return
            
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendPhoto"
            
            files = {
                'photo': ('chart.png', photo_buffer, 'image/png')
            }
            
            data = {
                'chat_id': self.chat_id,
                'caption': caption
            }
            
            response = requests.post(url, files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                print(f"✅ 图表已发送到Telegram")
            else:
                print(f"❌ 发送图表失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 发送Telegram图片失败: {e}")
    
    def print_current_status(self, data: Dict):
        """打印当前状态"""
        timestamp = datetime.fromisoformat(data['timestamp']).strftime("%H:%M:%S")
        
        print(f"[{timestamp}] 💰 {self.token_query}")
        print(f"  价格: ${data['price_usd']:.6f}")
        print(f"  流动性: ${data['total_liquidity_usd']:,.2f}")
        print(f"  24h交易量: ${data['total_volume_24h_usd']:,.2f}")
        print(f"  活跃交易对: {data['active_pairs']}")
        
        # 显示变化趋势
        if len(self.historical_data) >= 2:
            prev_data = self.historical_data[-2]
            price_change = self._calculate_percentage_change(
                data['price_usd'], prev_data['price_usd']
            )
            liquidity_change = self._calculate_percentage_change(
                data['total_liquidity_usd'], prev_data['total_liquidity_usd']
            )
            
            price_arrow = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            liquidity_arrow = "📈" if liquidity_change > 0 else "📉" if liquidity_change < 0 else "➡️"
            
            print(f"  价格变化: {price_arrow} {price_change:+.2f}%")
            print(f"  流动性变化: {liquidity_arrow} {liquidity_change:+.2f}%")
        
        print("-" * 50)
    
    async def run_monitor(self):
        """运行监控循环"""
        print(f"🚀 开始监控 {self.token_query} 池子价值...")
        print(f"📊 数据文件: {self.data_file}")
        print(f"🤖 Telegram Bot: {'已配置' if self.chat_id else '未配置 (仅控制台显示)'}")
        print("💡 提示: 设置 chat_id 参数来接收Telegram通知")
        print("=" * 50)
        
        consecutive_errors = 0
        max_errors = 10
        
        while True:
            try:
                # 获取当前数据
                current_data = self.get_current_pool_data()
                
                if current_data:
                    # 保存数据
                    self._save_data(current_data)
                    
                    # 打印状态
                    self.print_current_status(current_data)
                    
                    # 检查警报条件
                    alerts = self.check_alert_conditions(current_data)
                    
                    # 发送警报
                    for rule_name, message in alerts:
                        await self.send_telegram_alert(message)
                    
                    # 检查是否需要发送1分钟报告
                    if self.should_send_minute_report():
                        minute_report = self.generate_minute_report(current_data)
                        await self.send_telegram_alert(minute_report)
                        print("📊 已发送1分钟报告")
                    
                    # 检查是否需要发送图表（每小时）
                    if self.should_send_chart():
                        chart_buffer = self.generate_liquidity_chart(6)  # 6小时数据
                        if chart_buffer:
                            current_time = datetime.now().strftime('%H:%M')
                            caption = f"📊 {self.token_query} 流动性数据图表\n⏰ 生成时间: {current_time}\n📈 显示最近6小时数据"
                            await self.send_telegram_photo(chart_buffer, caption)
                            print("📈 已发送流动性图表")
                    
                    consecutive_errors = 0
                else:
                    consecutive_errors += 1
                    print(f"⚠️ 获取数据失败 ({consecutive_errors}/{max_errors})")
                    
                    if consecutive_errors >= max_errors:
                        error_msg = f"❌ 连续{max_errors}次获取数据失败，监控暂停"
                        print(error_msg)
                        await self.send_telegram_alert(error_msg)
                        break
                
                # 等待1秒
                await asyncio.sleep(1)
                
            except KeyboardInterrupt:
                print("\n👋 监控已停止")
                break
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                consecutive_errors += 1
                await asyncio.sleep(5)  # 错误时等待5秒

def main():
    """主函数"""
    print("=" * 60)
    print("🔥 池子价值监控警报系统")
    print("=" * 60)
    print("📝 使用说明:")
    print("1. 修改代码中的 chat_id 参数来接收Telegram通知")
    print("2. 修改 token_query 参数来监控不同的代币")
    print("3. 使用 Ctrl+C 安全停止监控")
    print("=" * 60)
    
    # 创建并运行监控器
    monitor = PoolMonitorAlert()
    
    try:
        asyncio.run(monitor.run_monitor())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")

if __name__ == "__main__":
    main()