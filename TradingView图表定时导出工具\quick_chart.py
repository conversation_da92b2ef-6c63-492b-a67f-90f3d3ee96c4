import time
import os
import logging
import schedule
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 数据存储目录
DATA_IMAGES_DIR = "data_Images"

# 内置HTML模板
HTML_TEMPLATE = '''<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Chart for Automation</title>
    <style>
        body, html { 
            margin: 0; 
            padding: 0; 
            overflow: hidden; 
            background-color: #131722;
        }
        #chart_container {
            border: none;
            outline: none;
        }
    </style>
</head>
<body>
    <div id="chart_container" style="width: 720px; height: 480px;"></div>
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        window.addEventListener('load', function() {
            new TradingView.widget({
                "width": 720,
                "height": 480,
                "autosize": false,
                "container_id": "chart_container",
                "symbol": "CRYPTO:PYTHIAUSD",
                "interval": "60",
                "locale": "zh_CN",
                "theme": "dark",
                "hide_top_toolbar": true,
                "hide_side_toolbar": true,
                "hide_legend": true,
                "hide_volume": true,
                "disabled_features": [
                    "use_localstorage_for_settings",
                    "volume_force_overlay",
                    "create_volume_indicator_by_default",
                    "header_widget",
                    "timeframes_toolbar",
                    "edit_buttons_in_legend",
                    "context_menus",
                    "left_toolbar",
                    "control_bar",
                    "border_around_the_chart"
                ],
                "overrides": {
                    "paneProperties.background": "#131722",
                    "paneProperties.vertGridProperties.color": "#363c4e",
                    "paneProperties.horzGridProperties.color": "#363c4e",
                    "paneProperties.rightMargin": 15,
                    "paneProperties.leftMargin": 5,
                    "mainSeriesProperties.candleStyle.upColor": "#26a69a",
                    "mainSeriesProperties.candleStyle.downColor": "#ef5350",
                    "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
                    "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
                    "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
                    "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350",
                    "scalesProperties.textColor": "#d1d4dc",
                    "scalesProperties.backgroundColor": "#131722"
                },
                "onChartReady": function() {
                    console.log("TradingView chart loaded successfully");
                }
            });
        });
    </script>
</body>
</html>'''

def ensure_data_images_dir():
    """确保data_Images目录存在"""
    if not os.path.exists(DATA_IMAGES_DIR):
        os.makedirs(DATA_IMAGES_DIR)
        logging.info(f"📁 创建目录: {DATA_IMAGES_DIR}")

def generate_timestamp_filename(symbol, extension="png"):
    """生成带时间戳的文件名"""
    # 清理交易对名称，移除特殊字符
    clean_symbol = symbol.replace(":", "_").replace("/", "_")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{clean_symbol}_{timestamp}.{extension}"
    return os.path.join(DATA_IMAGES_DIR, filename)

def quick_export_chart(symbol="CRYPTO:BTCUSD", output_path=None, template_content=None):
    """
    智能检测HTML界面尺寸并自动调整截图区域
    """
    
    # 确保输出目录存在
    ensure_data_images_dir()
    
    # 如果没有指定输出路径，使用时间戳命名
    if output_path is None:
        output_path = generate_timestamp_filename(symbol)
    elif not os.path.dirname(output_path):
        # 如果只提供了文件名，放到data_Images目录下
        output_path = os.path.join(DATA_IMAGES_DIR, output_path)
    
    # 1. 使用内置HTML模板或提供的模板内容
    if template_content is None:
        template_content = HTML_TEMPLATE

    # 2. 替换交易对符号
    html_content = template_content.replace("CRYPTO:PYTHIAUSD", symbol)
    
    temp_html_file = "temp_chart.html"
    with open(temp_html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    # Chrome配置 - 使用更大的窗口来容纳内容
    options = webdriver.ChromeOptions()
    options.add_argument("--headless")
    options.add_argument("--window-size=1000,800")  # 更大的窗口
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--force-device-scale-factor=1")
    
    service = ChromeService(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    try:
        # 打开页面
        driver.get("file://" + os.path.abspath(temp_html_file))
        
        # 等待iframe加载
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "iframe"))
        )
        time.sleep(8)
        
        # 智能检测实际尺寸
        chart_element = driver.find_element(By.ID, "chart_container")
        
        # 获取元素的实际尺寸和位置
        element_size = chart_element.size
        element_location = chart_element.location
        
        logging.info(f"📐 检测到图表容器尺寸: {element_size}")
        logging.info(f"📍 图表容器位置: {element_location}")
        
        # 获取页面实际尺寸
        page_width = driver.execute_script("return document.body.scrollWidth")
        page_height = driver.execute_script("return document.body.scrollHeight")
        logging.info(f"📏 页面实际尺寸: {page_width}x{page_height}")
        
        # 检查iframe内部的实际图表尺寸
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        if iframes:
            iframe = iframes[0]
            iframe_size = iframe.size
            logging.info(f"🖼️  iframe尺寸: {iframe_size}")
            
            # 切换到iframe检查内部尺寸
            driver.switch_to.frame(iframe)
            try:
                # 检查iframe内部的实际内容尺寸
                inner_width = driver.execute_script("return document.body.scrollWidth")
                inner_height = driver.execute_script("return document.body.scrollHeight")
                logging.info(f"📊 iframe内部尺寸: {inner_width}x{inner_height}")
                
                # 寻找实际的图表canvas或容器
                canvas_elements = driver.find_elements(By.TAG_NAME, "canvas")
                if canvas_elements:
                    canvas_size = canvas_elements[0].size
                    logging.info(f"🎨 Canvas尺寸: {canvas_size}")
                
            except Exception as e:
                logging.warning(f"⚠️  无法检测iframe内部尺寸: {e}")
            finally:
                driver.switch_to.default_content()
        
        # 智能截图策略 - 根据实际Canvas尺寸调整
        target_width = 720
        target_height = 480
        
        # 检查Canvas的实际尺寸
        canvas_elements = driver.find_elements(By.TAG_NAME, "canvas")
        if canvas_elements:
            # 切换到iframe获取Canvas
            driver.switch_to.frame(iframes[0])
            canvas_elements = driver.find_elements(By.TAG_NAME, "canvas")
            if canvas_elements:
                canvas = canvas_elements[0]
                canvas_size = canvas.size
                logging.info(f"🎯 将截取Canvas区域: {canvas_size}")
                
                # 如果Canvas尺寸不是720x480，尝试调整
                if canvas_size['width'] < target_width or canvas_size['height'] < target_height:
                    logging.info("🔧 Canvas尺寸小于目标，尝试调整...")
                    
                    # 尝试通过JavaScript调整Canvas尺寸
                    try:
                        driver.execute_script(f"""
                            var canvases = document.querySelectorAll('canvas');
                            canvases.forEach(function(canvas) {{
                                canvas.style.width = '{target_width}px';
                                canvas.style.height = '{target_height}px';
                            }});
                        """)
                        time.sleep(2)
                        canvas_size = canvas.size
                        logging.info(f"🔧 调整后Canvas尺寸: {canvas_size}")
                    except Exception as e:
                        logging.warning(f"⚠️  无法调整Canvas尺寸: {e}")
                
                # 截取Canvas区域
                try:
                    canvas.screenshot(output_path)
                    logging.info("📸 使用Canvas截图")
                    driver.switch_to.default_content()
                except Exception as e:
                    logging.warning(f"⚠️  Canvas截图失败: {e}")
                    driver.switch_to.default_content()
                    # 回退到容器截图
                    chart_element.screenshot(output_path)
                    logging.info("📸 回退到容器截图")
            else:
                driver.switch_to.default_content()
                chart_element.screenshot(output_path)
                logging.info("📸 使用容器截图")
        else:
            # 如果没有Canvas，使用容器截图
            chart_element.screenshot(output_path)
            logging.info("📸 使用容器截图")
        
        # 验证生成的图片
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            logging.info(f"✅ 导出完成: {output_path} (文件大小: {file_size} bytes)")
            return True
        else:
            logging.error("❌ 截图文件未生成")
            return False
        
    except Exception as e:
        logging.error(f"❌ 导出失败: {e}")
        return False
    finally:
        driver.quit()
        if os.path.exists(temp_html_file):
            os.remove(temp_html_file)

def scheduled_export():
    """定时导出任务"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n⏰ [{current_time}] 开始定时导出任务")
    
    symbol = "CRYPTO:PYTHIAUSD"
    
    print(f"🚀 正在导出: {symbol}")
    success = quick_export_chart(symbol)  # 使用时间戳自动命名
    if success:
        print(f"✅ {symbol} 导出完成")
    else:
        print(f"❌ {symbol} 导出失败")
    
    print(f"📊 本次任务完成，下次执行时间: {datetime.now().strftime('%H:%M:%S')} + 1分钟\n")

def batch_quick_export():
    """批量快速导出（一次性）"""
    
    symbols = [
        "CRYPTO:PYTHIAUSD"
    ]
    
    for symbol in symbols:
        print(f"🚀 正在导出: {symbol}")
        success = quick_export_chart(symbol)
        if success:
            print(f"✅ {symbol} 完成")
        else:
            print(f"❌ {symbol} 失败")
        print()

def start_scheduler():
    """启动定时任务"""
    print("🕐 TradingView图表定时导出工具启动")
    print("📅 任务间隔: 每1分钟")
    print("📁 存储目录: data_Images/")
    print("🏷️  文件命名: 交易对_时间戳.png")
    print("=" * 50)
    
    # 设置定时任务 - 每1分钟执行一次
    schedule.every(1).minutes.do(scheduled_export)
    
    # 立即执行一次
    print("🚀 立即执行首次导出...")
    scheduled_export()
    
    # 开始定时循环
    print("⏰ 定时任务已启动，按 Ctrl+C 停止")
    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 定时任务已停止")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "once":
            # 一次性导出
            batch_quick_export()
        elif sys.argv[1] == "schedule":
            # 定时任务模式
            start_scheduler()
        else:
            print("用法:")
            print("  python quick_chart.py          # 默认启动定时任务")
            print("  python quick_chart.py once     # 一次性导出")
            print("  python quick_chart.py schedule # 定时任务模式")
    else:
        # 默认启动定时任务
        start_scheduler()