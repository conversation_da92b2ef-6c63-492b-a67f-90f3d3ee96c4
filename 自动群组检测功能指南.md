# 自动群组检测功能指南

## 🎯 功能概述

自动群组检测功能让Bot能够：
- 自动识别和记录活跃群组
- 无需手动配置群组ID
- 支持多群组同时发送报告
- 动态管理群组列表

## 🔧 配置说明

### config.py 配置
```python
"telegram": {
    "bot_token": "你的Bot Token",
    "chat_id": "AUTO_DETECT",        # 启用自动检测
    "auto_group_mode": True,         # 开启自动群组模式
    "fallback_chat_id": "-2681225020" # 备用群组ID
}
```

## 🚀 使用流程

### 1. 添加Bot到群组
- 将 @pythia_is_ai_bot 添加到目标群组
- 确保Bot有发送消息权限

### 2. 激活群组记录
在群组中发送：
```
@pythia_is_ai_bot 你好
```

### 3. 自动记录
- Bot收到@消息后自动记录群组ID
- 群组信息保存到 data/active_groups.json

### 4. 自动发送
- 生成报告时自动发送到所有活跃群组
- 无需额外配置

## 📱 群组管理

### 查看活跃群组
活跃群组信息保存在：`data/active_groups.json`

```json
{
  "groups": ["-2681225020", "-1001234567890"],
  "last_updated": "2024-01-01T12:00:00",
  "group_info": {
    "-2681225020": {
      "name": "PYTHIA群组",
      "last_active": "2024-01-01T12:00:00"
    }
  }
}
```

### 移除群组
如需移除某个群组，编辑 `data/active_groups.json` 文件，从 `groups` 数组中删除对应ID。

## 🔄 工作模式

### 自动群组模式 (推荐)
```python
auto_group_mode: True
chat_id: "AUTO_DETECT"
```
- 发送到所有活跃群组
- 动态管理群组列表

### 传统模式
```python
auto_group_mode: False
chat_id: "具体群组ID"
```
- 只发送到指定群组
- 需要手动配置

## 🛡️ 备用机制

如果没有活跃群组记录，系统会：
1. 使用 `fallback_chat_id` 作为备用群组
2. 确保报告能够正常发送

## 💡 最佳实践

1. **初次设置**：在主要群组中@Bot激活记录
2. **多群组管理**：在每个目标群组中都@Bot一次
3. **定期检查**：查看 `data/active_groups.json` 确认群组列表
4. **备用配置**：设置 `fallback_chat_id` 作为保障

## 🔍 故障排除

### Bot不记录群组
- 检查Bot是否有群组发送权限
- 确认消息格式：`@pythia_is_ai_bot 消息内容`
- 查看Bot日志确认是否收到消息

### 报告不发送
- 检查 `ENABLE_TELEGRAM_SEND = True`
- 确认 `auto_group_mode = True`
- 验证活跃群组文件是否存在

### 多群组发送失败
- 检查每个群组的Bot权限
- 确认群组ID格式正确
- 查看发送日志定位具体问题

## 📊 监控和日志

系统会记录：
- 群组记录操作
- 发送成功/失败状态
- 活跃群组更新时间

查看日志了解详细运行状态。
