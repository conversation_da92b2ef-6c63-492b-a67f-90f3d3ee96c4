#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Telegram发送开关功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_telegram_switch():
    """测试Telegram开关设置"""
    print("🔧 测试Telegram发送开关功能")
    print("=" * 50)
    
    try:
        # 读取文件内容
        with open("pythia_integrated_complete.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查开关变量
        if "ENABLE_TELEGRAM_SEND = False" in content:
            print("✅ Telegram发送已关闭")
            print("   当前设置: ENABLE_TELEGRAM_SEND = False")
        elif "ENABLE_TELEGRAM_SEND = True" in content:
            print("🔔 Telegram发送已开启")
            print("   当前设置: ENABLE_TELEGRAM_SEND = True")
        else:
            print("❌ 未找到开关变量")
            return False
        
        # 检查修改位置
        modifications = [
            "if ENABLE_TELEGRAM_SEND and self.telegram_bot_token and self.telegram_chat_id:",
            "if ENABLE_TELEGRAM_SEND and analyzer.telegram_bot_token and analyzer.telegram_chat_id:",
            "if not ENABLE_TELEGRAM_SEND:",
            "print(\"🔇 Telegram发送已关闭\")"
        ]
        
        print(f"\n🔍 检查代码修改:")
        all_found = True
        for mod in modifications:
            if mod in content:
                print(f"   ✅ 找到: {mod[:50]}...")
            else:
                print(f"   ❌ 缺失: {mod[:50]}...")
                all_found = False
        
        # 显示开关使用说明
        print(f"\n📋 开关使用说明:")
        print(f"   🔇 关闭发送: ENABLE_TELEGRAM_SEND = False")
        print(f"   🔔 开启发送: ENABLE_TELEGRAM_SEND = True")
        print(f"   📍 修改位置: 文件第27行左右")
        
        return all_found
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_switch_instructions():
    """显示开关操作说明"""
    print(f"\n📖 操作说明:")
    print(f"=" * 50)
    
    print(f"🔧 如何关闭Telegram发送:")
    print(f"   1. 打开 pythia_integrated_complete.py")
    print(f"   2. 找到第27行: ENABLE_TELEGRAM_SEND = False")
    print(f"   3. 确保设置为 False (当前已设置)")
    print(f"   4. 保存文件")
    
    print(f"\n🔔 如何重新开启Telegram发送:")
    print(f"   1. 打开 pythia_integrated_complete.py")
    print(f"   2. 找到第27行: ENABLE_TELEGRAM_SEND = False")
    print(f"   3. 改为: ENABLE_TELEGRAM_SEND = True")
    print(f"   4. 保存文件")
    
    print(f"\n💡 运行效果:")
    print(f"   关闭时显示: 🔇 Telegram发送已关闭")
    print(f"   开启时显示: ✅ 图片已成功发送到Telegram群组")
    
    print(f"\n🎯 修改的位置:")
    print(f"   1. 第1492行: save_report_to_file方法中的发送逻辑")
    print(f"   2. 第2544行: 监控模式中的发送逻辑")
    print(f"   3. 两处都添加了ENABLE_TELEGRAM_SEND检查")

def create_switch_guide():
    """创建开关操作指南"""
    guide_content = """# Telegram发送开关操作指南

## 🎯 快速操作

### 关闭Telegram发送
```python
# 在 pythia_integrated_complete.py 第27行
ENABLE_TELEGRAM_SEND = False  # 设置为False关闭
```

### 开启Telegram发送
```python
# 在 pythia_integrated_complete.py 第27行
ENABLE_TELEGRAM_SEND = True   # 设置为True开启
```

## 📍 修改位置

文件: `pythia_integrated_complete.py`
行号: 第27行左右

```python
# 🔧 Telegram发送开关 - 设置为False关闭发送功能
ENABLE_TELEGRAM_SEND = False  # 改为True开启，False关闭
```

## 🔍 影响的功能

### 1. 报告生成时的自动发送
- 位置: `save_report_to_file` 方法
- 效果: 关闭后不会自动发送图片到群组

### 2. 监控模式的自动发送
- 位置: `monitor_html_files` 函数
- 效果: 关闭后监控到新文件也不会发送

## 💬 运行时的提示信息

### 关闭状态
```
🔇 Telegram发送已关闭
```

### 开启状态
```
✅ 图片已成功发送到Telegram群组
```

## 🔄 无需重启

修改开关后无需重启程序，下次运行时自动生效。

## 🛡️ 安全性

- 即使开关开启，如果没有配置bot_token和chat_id，也不会发送
- 开关只是额外的控制层，不影响其他功能
- 关闭发送不影响图片生成和本地保存

## 📝 使用建议

1. **测试阶段**: 建议关闭，避免频繁发送测试消息
2. **生产环境**: 根据需要开启或关闭
3. **临时关闭**: 不需要修改配置文件，只需改这一个开关
"""
    
    try:
        with open("Telegram发送开关指南.md", "w", encoding="utf-8") as f:
            f.write(guide_content)
        print(f"\n📄 已创建操作指南: Telegram发送开关指南.md")
        return True
    except Exception as e:
        print(f"❌ 创建指南失败: {e}")
        return False

if __name__ == "__main__":
    success = test_telegram_switch()
    
    if success:
        show_switch_instructions()
        create_switch_guide()
        
        print(f"\n🎉 Telegram发送开关配置成功！")
        print(f"🔇 当前状态: 已关闭发送功能")
        print(f"💡 需要开启时，只需将 ENABLE_TELEGRAM_SEND 改为 True")
    else:
        print(f"\n❌ 开关配置失败，请检查代码修改")
