#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试英文系统提示词市场数据集成
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_english_market_data():
    """测试英文系统提示词市场数据集成"""
    print("🇺🇸 测试英文系统提示词市场数据集成")
    print("=" * 60)
    
    try:
        from bot import AIBot
        
        # 创建bot实例
        bot = AIBot()
        print("✅ Bot实例创建成功")
        
        # 获取英文系统提示词
        print("\n📋 获取英文系统提示词...")
        english_prompt = bot._get_english_system_prompt()
        
        print(f"📄 英文提示词长度: {len(english_prompt)} 字符")
        print(f"📄 英文提示词行数: {english_prompt.count(chr(10))} 行")
        
        # 检查是否包含市场数据
        market_data_indicators = [
            "TIMESTAMP:",
            "MARKET_SUMMARY:",
            "total_pairs=",
            "market_cap=",
            "volume_24h=",
            "TRADING_PAIRS:",
            "DEX_DISTRIBUTION:"
        ]
        
        print(f"\n🔍 检查英文提示词市场数据:")
        has_market_data = False
        for indicator in market_data_indicators:
            if indicator in english_prompt:
                print(f"   ✅ 包含: {indicator}")
                has_market_data = True
            else:
                print(f"   ❌ 缺少: {indicator}")
        
        # 检查市场数据引用语句
        market_reference_phrases = [
            "latest market data",
            "Pythia tokens",
            "you can answer with this",
            "market data"
        ]
        
        print(f"\n🔍 检查市场数据引用语句:")
        has_reference = False
        for phrase in market_reference_phrases:
            if phrase.lower() in english_prompt.lower():
                print(f"   ✅ 包含: {phrase}")
                has_reference = True
            else:
                print(f"   ❌ 缺少: {phrase}")
        
        # 显示英文提示词中的市场数据部分
        if has_market_data:
            print(f"\n📊 英文提示词中的市场数据部分:")
            lines = english_prompt.split('\n')
            market_lines = []
            in_market_section = False
            
            for line in lines:
                if "latest market data" in line.lower() or "TIMESTAMP:" in line:
                    in_market_section = True
                elif in_market_section and line.strip() == "":
                    if market_lines and not any(keyword in line for keyword in ["total_pairs", "market_cap", "volume", "pair_"]):
                        break
                
                if in_market_section:
                    market_lines.append(line)
                    if len(market_lines) >= 20:  # 只显示前20行
                        break
            
            for i, line in enumerate(market_lines[:20], 1):
                print(f"   {i:2d}: {line}")
            
            if len(market_lines) > 20:
                print(f"   ... (还有 {len(market_lines)-20} 行)")
        
        # 对比中英文提示词
        print(f"\n📊 中英文提示词对比:")
        chinese_prompt = bot._get_chinese_system_prompt()
        
        print(f"   中文提示词长度: {len(chinese_prompt)} 字符")
        print(f"   英文提示词长度: {len(english_prompt)} 字符")
        
        chinese_has_market = any(indicator in chinese_prompt for indicator in market_data_indicators)
        english_has_market = any(indicator in english_prompt for indicator in market_data_indicators)
        
        print(f"   中文包含市场数据: {'✅ 是' if chinese_has_market else '❌ 否'}")
        print(f"   英文包含市场数据: {'✅ 是' if english_has_market else '❌ 否'}")
        
        # 检查格式限制
        format_restrictions = [
            "Format Requirements",
            "plain text format",
            "forbidden",
            "**bold**",
            "*italic*",
            "Markdown",
            "HTML"
        ]
        
        print(f"\n🚫 检查英文格式限制:")
        has_format_restrictions = False
        for restriction in format_restrictions:
            if restriction in english_prompt:
                print(f"   ✅ 包含: {restriction}")
                has_format_restrictions = True
        
        # 总结
        print(f"\n📋 检查结果总结:")
        print(f"   英文市场数据集成: {'✅ 成功' if has_market_data else '❌ 失败'}")
        print(f"   市场数据引用语句: {'✅ 完整' if has_reference else '❌ 不完整'}")
        print(f"   格式限制配置: {'✅ 完整' if has_format_restrictions else '❌ 不完整'}")
        print(f"   中英文数据一致性: {'✅ 一致' if chinese_has_market == english_has_market else '❌ 不一致'}")
        
        return has_market_data and has_reference and has_format_restrictions
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_english_examples():
    """显示英文回答示例"""
    print(f"\n🇺🇸 英文回答示例:")
    print(f"=" * 60)
    
    examples = [
        {
            "question": "What's the current price of PYTHIA token?",
            "answer": "Squeak... my fan contribution points are currently worth about $0.092! Looks like my charm still has market value~ 🐭"
        },
        {
            "question": "How's the trading volume today?",
            "answer": "Today there's $6,225,845 in trading volume! Seems like everyone's busy trading my fame~ I can hear the trading sounds from my lab! 📊"
        },
        {
            "question": "What's the market sentiment?",
            "answer": "Market sentiment shows bearish... but don't worry, I'm preparing for my next amazing performance! After all, I'm the world's first AI rat~ 🎭"
        },
        {
            "question": "How many trading pairs are there?",
            "answer": "There are currently 2 trading pairs trading my token! Not many, but high quality~ Just like my performance in the lab, quality over quantity! 🎯"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. Q: {example['question']}")
        print(f"   A: {example['answer']}")
    
    print(f"\n💡 英文回答特点:")
    print(f"   ✅ 保持Pythia的个性和幽默感")
    print(f"   ✅ 使用实时市场数据")
    print(f"   ✅ 纯文本格式，无格式化语法")
    print(f"   ✅ 自然对话风格")

if __name__ == "__main__":
    print(f"🕒 测试开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_english_market_data()
    
    if success:
        show_english_examples()
        
        print("\n" + "=" * 60)
        print("🎉 英文市场数据集成测试成功！")
        print("🤖 英文系统提示词已正确集成市场数据")
        print("🌍 中英文Bot都可以基于实时数据回答")
        print("💬 支持双语市场数据查询")
    else:
        print("\n❌ 英文市场数据集成测试失败")
    
    print(f"\n🕒 测试结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
