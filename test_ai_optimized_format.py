#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI优化的数据格式
验证新的data_txt文件夹和AI友好的数据格式
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from pythia_integrated_complete import PythiaIntegratedAnalyzer
    print("✅ 成功导入PythiaIntegratedAnalyzer")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_ai_optimized_format():
    """测试AI优化的数据格式"""
    print("🤖 测试AI优化数据格式")
    print("=" * 60)
    
    try:
        # 创建分析器实例
        analyzer = PythiaIntegratedAnalyzer()
        print("✅ 分析器实例创建成功")
        
        # 获取数据
        print("\n📊 获取PYTHIA数据...")
        pairs_data, filter_stats = analyzer.search_all_pythia_pairs()
        
        if not pairs_data:
            print("❌ 无法获取数据")
            return False
            
        print(f"✅ 获取到 {len(pairs_data)} 个交易对")
        
        # 分析数据
        print("\n🔍 分析数据...")
        formatted_data = {"pairs": pairs_data}
        analysis = analyzer.analyze_price_data(formatted_data)
        sentiment = analyzer.analyze_market_sentiment(pairs_data)
        metrics = analyzer.calculate_comprehensive_metrics(pairs_data, analysis)
        
        # 测试AI优化格式保存
        print("\n💾 测试AI优化格式保存...")
        txt_filepath = analyzer.save_api_data_to_txt(pairs_data, analysis, sentiment, metrics)
        
        if txt_filepath and os.path.exists(txt_filepath):
            print(f"✅ AI优化数据保存成功: {txt_filepath}")
            
            # 分析文件内容
            with open(txt_filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📊 文件分析:")
            print(f"   文件大小: {len(content):,} 字符")
            print(f"   行数: {content.count(chr(10)):,} 行")
            
            # 检查关键部分
            sections = {
                "TIMESTAMP": "时间戳" in content or "TIMESTAMP" in content,
                "MARKET_SUMMARY": "MARKET_SUMMARY" in content,
                "PRICE_RANGE": "PRICE_RANGE" in content,
                "DEX_DISTRIBUTION": "DEX_DISTRIBUTION" in content,
                "TRADING_PAIRS": "TRADING_PAIRS" in content,
                "RAW_DATA": "RAW_DATA" in content
            }
            
            print(f"\n🔍 数据结构检查:")
            for section, exists in sections.items():
                status = "✅" if exists else "❌"
                print(f"   {status} {section}")
            
            # 显示文件开头内容
            print(f"\n📄 文件内容预览 (前30行):")
            lines = content.split('\n')
            for i, line in enumerate(lines[:30], 1):
                print(f"   {i:2d}: {line}")
            
            if len(lines) > 30:
                print(f"   ... (还有 {len(lines)-30} 行)")
            
            return True
        else:
            print("❌ AI优化数据保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_token_efficiency():
    """分析token效率"""
    print("\n📈 分析Token效率...")
    
    # 检查data_txt文件夹
    data_txt_dir = "data_txt"
    if not os.path.exists(data_txt_dir):
        print("❌ data_txt文件夹不存在")
        return
    
    # 获取最新文件
    files = [f for f in os.listdir(data_txt_dir) if f.endswith('.txt')]
    if not files:
        print("❌ 没有找到txt文件")
        return
    
    latest_file = max(files, key=lambda f: os.path.getctime(os.path.join(data_txt_dir, f)))
    filepath = os.path.join(data_txt_dir, latest_file)
    
    print(f"📁 分析文件: {latest_file}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Token效率分析
    total_chars = len(content)
    total_lines = content.count('\n')
    
    # 估算token数量 (粗略估算: 1 token ≈ 4 字符)
    estimated_tokens = total_chars // 4
    
    # 分析数据密度
    data_sections = content.count('=')  # 数据分隔符
    structured_lines = content.count(':')  # 结构化数据行
    
    print(f"\n📊 Token效率分析:")
    print(f"   总字符数: {total_chars:,}")
    print(f"   总行数: {total_lines:,}")
    print(f"   估算Token数: {estimated_tokens:,}")
    print(f"   结构化数据行: {structured_lines:,}")
    print(f"   数据密度: {structured_lines/total_lines*100:.1f}%")
    
    # 检查是否包含无关元素
    decorative_chars = content.count('📊') + content.count('💰') + content.count('🏪')
    if decorative_chars == 0:
        print("   ✅ 无装饰性字符，适合AI处理")
    else:
        print(f"   ⚠️ 包含 {decorative_chars} 个装饰性字符")
    
    # 数据完整性检查
    has_raw_data = "RAW_DATA" in content
    has_structured_data = "MARKET_SUMMARY" in content
    
    print(f"\n🔍 数据完整性:")
    print(f"   ✅ 结构化数据: {'是' if has_structured_data else '否'}")
    print(f"   ✅ 原始数据: {'是' if has_raw_data else '否'}")

if __name__ == "__main__":
    print(f"🕒 测试开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试AI优化格式
    success = test_ai_optimized_format()
    
    if success:
        # 分析token效率
        analyze_token_efficiency()
        
        print("\n" + "=" * 60)
        print("🎉 AI优化格式测试完成！")
        print("💡 新格式特点:")
        print("   - 保存到 data_txt/ 文件夹")
        print("   - 结构化键值对格式，便于AI解析")
        print("   - 去除装饰性元素，降低token消耗")
        print("   - 保持数据完整性，不删减任何信息")
        print("   - 压缩JSON格式存储原始数据")
    else:
        print("\n❌ 测试失败，请检查配置")
    
    print(f"\n🕒 测试结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
