#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本格式清理器 - 专门清理AI回答中的格式化语法
按照用户要求的顺序进行替换
"""

import re

def clean_format_text(text: str) -> str:
    """
    按照指定顺序清理文本格式
    1. 首先替换 "*   " (列表项开头)
    2. 然后替换 "**" (粗体标记)
    """
    if not text:
        return text
    
    cleaned_text = text
    
    # 第一步：替换 "*   " (列表项开头的星号和空格)
    # 匹配行首的 "*   " 并替换为空
    cleaned_text = re.sub(r'^\s*\*\s+', '', cleaned_text, flags=re.MULTILINE)
    
    # 第二步：替换 "**" (粗体标记)
    # 匹配 **文字** 格式并只保留文字内容
    cleaned_text = re.sub(r'\*\*(.*?)\*\*', r'\1', cleaned_text)
    
    # 额外清理其他常见格式（可选）
    # 清理单个星号的斜体
    cleaned_text = re.sub(r'(?<!\*)\*([^*]+?)\*(?!\*)', r'\1', cleaned_text)
    
    # 清理代码格式
    cleaned_text = re.sub(r'`([^`]+?)`', r'\1', cleaned_text)
    
    # 清理多余的空行
    cleaned_text = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_text)
    
    # 清理行首行尾多余空白
    lines = cleaned_text.split('\n')
    cleaned_lines = [line.strip() for line in lines if line.strip()]
    cleaned_text = '\n'.join(cleaned_lines)
    
    return cleaned_text

def test_cleaner():
    """测试清理器效果"""
    test_text = """吱... 🐭💨 哈哈 又要我仔细分析我的"粉丝贡献点"的市场情况啦？没问题 我的AI网友已经把所有数据都"吱"给我了！让我再给你们好好"吱"一下！

**我的"名气"总览：**

**总身价**：现在我的所有贡献点加起来 大概有9258万多美元呢！吱！我的魅力还是很有价值的！✨
*   **24小时交易量**：过去一天 大家交易我的名气 交易量达到了622万多美元！好多人在为我贡献呢！
*   **能换好吃的的钱**：目前有1275万多美元的贡献点可以随时换成好吃的！我的储备粮很充足！😋
*   **买卖情况**：买我名气的人有11273个 卖我名气的人有11230个。嗯... 买的人比卖的人稍微多一点点！大家都很喜欢我！
*   **我的心情指数**：AI网友说现在大家对我的心情有点悲观📉。可能是我最近在五星级酒店（Neiry实验室）里睡得太香了 没出来多露两爪子吧！
*   **魅力值变化**：我的贡献点平均价格下降了6.5%左右。没关系 这是在蓄力 等我下次回答个更难的问题 肯定就"吱溜"一下涨回来了！

**今天的"贡献点"价格区间：**

*   我的贡献点今天在0.09258美元到0.09275美元之间"吱溜"来"吱溜"去。就像我跑轮子一样 有点小幅度摆动 正常啦！"""
    
    print("🧹 文本格式清理器测试")
    print("=" * 60)
    
    print("📝 原始文本:")
    print(test_text)
    print("\n" + "=" * 60)
    
    cleaned = clean_format_text(test_text)
    
    print("✨ 清理后文本:")
    print(cleaned)
    print("\n" + "=" * 60)
    
    # 统计清理效果
    original_lines = test_text.split('\n')
    cleaned_lines = cleaned.split('\n')
    
    star_list_count = len(re.findall(r'^\s*\*\s+', test_text, re.MULTILINE))
    bold_count = len(re.findall(r'\*\*(.*?)\*\*', test_text))
    
    print("📊 清理统计:")
    print(f"   原始行数: {len(original_lines)}")
    print(f"   清理后行数: {len(cleaned_lines)}")
    print(f"   清理的列表项: {star_list_count} 个")
    print(f"   清理的粗体: {bold_count} 个")
    print(f"   原始字符数: {len(test_text)}")
    print(f"   清理后字符数: {len(cleaned)}")
    print(f"   减少字符: {len(test_text) - len(cleaned)} 个")

def create_replacement_rules():
    """创建替换规则文档"""
    rules_content = """# 文本格式清理替换规则

## 🎯 替换顺序（重要！）

### 第一步：清理列表项
```
查找: ^\s*\*\s+
替换: (空)
说明: 清理行首的 "*   " 格式
```

### 第二步：清理粗体
```
查找: \*\*(.*?)\*\*
替换: $1
说明: 将 **文字** 替换为 文字
```

## 📝 具体替换示例

### 列表项清理
```
原文: *   **24小时交易量**：过去一天...
替换: **24小时交易量**：过去一天...
```

### 粗体清理
```
原文: **24小时交易量**：过去一天...
替换: 24小时交易量：过去一天...
```

### 完整效果
```
原文: *   **24小时交易量**：过去一天...
最终: 24小时交易量：过去一天...
```

## 🔧 在不同编辑器中的使用

### VS Code
1. 按 Ctrl+H 打开替换
2. 点击正则表达式按钮 (.*)
3. 第一次替换：
   - 查找: `^\s*\*\s+`
   - 替换: (留空)
4. 第二次替换：
   - 查找: `\*\*(.*?)\*\*`
   - 替换: `$1`

### Notepad++
1. 按 Ctrl+H 打开替换
2. 选择"正则表达式"模式
3. 第一次替换：
   - 查找: `^\s*\*\s+`
   - 替换: (留空)
4. 第二次替换：
   - 查找: `\*\*(.*?)\*\*`
   - 替换: `\1`

### Sublime Text
1. 按 Ctrl+H 打开替换
2. 点击正则表达式按钮
3. 使用与VS Code相同的规则

## 🐍 Python脚本使用

```python
import re

def clean_text(text):
    # 第一步：清理列表项
    text = re.sub(r'^\s*\*\s+', '', text, flags=re.MULTILINE)
    # 第二步：清理粗体
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
    return text

# 使用示例
original = "你的原始文本"
cleaned = clean_text(original)
print(cleaned)
```

## ⚠️ 注意事项

1. **顺序很重要**：必须先清理列表项，再清理粗体
2. **正则表达式**：确保编辑器开启正则表达式模式
3. **备份原文**：替换前建议备份原始文本
4. **逐步替换**：建议分两步进行，便于检查效果

## 🎯 预期效果

清理后的文本应该：
- 没有 `*   ` 开头的列表项
- 没有 `**粗体**` 格式
- 保持原有的内容和emoji
- 变成纯文本对话格式
"""
    
    try:
        with open("文本格式清理替换规则.md", "w", encoding="utf-8") as f:
            f.write(rules_content)
        print("📄 已创建替换规则文档: 文本格式清理替换规则.md")
        return True
    except Exception as e:
        print(f"❌ 创建文档失败: {e}")
        return False

if __name__ == "__main__":
    # 测试清理器
    test_cleaner()
    
    # 创建规则文档
    print("\n" + "=" * 60)
    create_replacement_rules()
    
    print("\n🎉 文本格式清理器准备完成！")
    print("💡 您可以使用以下方法清理文本：")
    print("   1. 运行此脚本进行自动清理")
    print("   2. 使用文档中的正则表达式手动替换")
    print("   3. 在编辑器中按顺序执行替换规则")
