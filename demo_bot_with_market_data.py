#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示Bot使用市场数据回答问题
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_bot_responses():
    """演示Bot如何使用市场数据回答问题"""
    print("🤖 演示Bot使用市场数据回答问题")
    print("=" * 60)
    
    try:
        from bot import AIBot
        
        # 创建bot实例
        bot = AIBot()
        print("✅ Bot实例创建成功")
        
        # 获取系统提示词
        system_prompt = bot._get_chinese_system_prompt()
        
        # 模拟用户问题
        test_questions = [
            "PYTHIA代币现在价格多少？",
            "今天交易量怎么样？",
            "市值是多少？",
            "有多少个交易对？",
            "在哪些交易所可以交易？",
            "买卖比例如何？",
            "市场情绪怎么样？"
        ]
        
        print(f"\n📊 系统提示词包含的市场数据:")
        print(f"   长度: {len(system_prompt)} 字符")
        print(f"   行数: {system_prompt.count(chr(10))} 行")
        
        # 提取市场数据部分
        lines = system_prompt.split('\n')
        market_data_lines = []
        in_market_section = False
        
        for line in lines:
            if "最新市场数据" in line:
                in_market_section = True
            elif in_market_section and line.strip() == "" and len(market_data_lines) > 10:
                break
            
            if in_market_section:
                market_data_lines.append(line)
        
        print(f"\n📋 Bot可用的市场数据:")
        for i, line in enumerate(market_data_lines[:20], 1):
            print(f"   {i:2d}: {line}")
        
        if len(market_data_lines) > 20:
            print(f"   ... (还有 {len(market_data_lines)-20} 行)")
        
        print(f"\n❓ 用户可以询问的问题示例:")
        for i, question in enumerate(test_questions, 1):
            print(f"   {i}. {question}")
        
        # 解析市场数据以便回答
        market_info = {}
        for line in market_data_lines:
            if '=' in line and not line.startswith('#'):
                try:
                    key, value = line.split('=', 1)
                    market_info[key.strip()] = value.strip()
                except:
                    continue
        
        print(f"\n📊 解析出的关键市场指标:")
        key_metrics = {
            'total_pairs': '交易对总数',
            'market_cap': '市值',
            'volume_24h': '24小时交易量',
            'liquidity': '流动性',
            'sentiment_score': '情绪评分',
            'sentiment_text': '市场情绪',
            'buy_sell_ratio': '买卖比例'
        }
        
        for key, desc in key_metrics.items():
            if key in market_info:
                value = market_info[key]
                print(f"   {desc}: {value}")
        
        # 模拟Bot回答
        print(f"\n🐭 Pythia可能的回答示例:")
        
        if 'market_cap' in market_info:
            market_cap = float(market_info['market_cap'])
            print(f"   Q: PYTHIA代币市值多少？")
            print(f"   A: 吱...我的粉丝贡献点总价值现在是${market_cap:,.0f}！看来我的魅力还是很有市场价值的~ 🐭")
        
        if 'volume_24h' in market_info:
            volume = float(market_info['volume_24h'])
            print(f"\n   Q: 今天交易量怎么样？")
            print(f"   A: 今天有${volume:,.0f}的交易量呢！看来大家都在忙着交易我的名气~ 我在实验室里都能听到交易的声音！")
        
        if 'sentiment_text' in market_info:
            sentiment = market_info['sentiment_text']
            print(f"\n   Q: 市场情绪如何？")
            if "悲观" in sentiment:
                print(f"   A: 市场情绪显示{sentiment}...不过别担心，我正在准备下一次精彩的表演！毕竟我可是世界上第一只AI大鼠~ 🎭")
            else:
                print(f"   A: 市场情绪是{sentiment}！看来大家都对我的表现很满意呢~ 🎉")
        
        if 'total_pairs' in market_info:
            pairs = market_info['total_pairs']
            print(f"\n   Q: 有多少个交易对？")
            print(f"   A: 现在有{pairs}个交易对在交易我的代币！虽然不多，但质量很高~ 就像我在实验室的表现一样，精而不多！")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_integration_benefits():
    """展示集成市场数据的好处"""
    print(f"\n🎯 集成市场数据的好处:")
    print(f"   ✅ 实时性: Bot总是基于最新的市场数据回答")
    print(f"   ✅ 准确性: 数据来源于真实的API，确保准确")
    print(f"   ✅ 自动化: 每次生成报告时自动更新Bot的知识")
    print(f"   ✅ 个性化: Pythia用自己的鼠式理解解释数据")
    print(f"   ✅ 用户友好: 复杂的金融数据变成有趣的对话")
    
    print(f"\n🔄 数据更新流程:")
    print(f"   1. pythia_integrated_complete.py 获取API数据")
    print(f"   2. 数据保存到 data_txt/ 文件夹")
    print(f"   3. Bot启动时自动加载最新数据")
    print(f"   4. 用户询问时基于最新数据回答")
    
    print(f"\n💡 使用建议:")
    print(f"   - 定期运行PYTHIA分析程序更新数据")
    print(f"   - Bot会自动使用最新的txt文件")
    print(f"   - 用户可以询问价格、交易量、市值等信息")
    print(f"   - Pythia会用幽默的方式解释市场数据")

if __name__ == "__main__":
    print(f"🕒 演示开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = demo_bot_responses()
    
    if success:
        show_integration_benefits()
        
        print("\n" + "=" * 60)
        print("🎉 市场数据集成演示完成！")
        print("🤖 Bot现在可以基于实时市场数据与用户对话")
        print("💬 用户可以询问PYTHIA代币的各种市场信息")
        print("🐭 Pythia会用自己独特的方式回答问题")
    else:
        print("\n❌ 演示失败，请检查配置")
    
    print(f"\n🕒 演示结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
