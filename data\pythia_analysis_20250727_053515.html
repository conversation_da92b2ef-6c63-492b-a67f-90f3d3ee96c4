<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=1280, initial-scale=1.0">
    <title>PYTHIA 分析报告 (最终复刻版)</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #0D1117;
            --bg-secondary: #101419;
            --bg-module: #161B22;
            --text-primary: #E6EDF3;
            --text-secondary: #B0BAC6;
            --text-muted: #7D8590;
            --accent-primary: #F2BC8C;
            --border-primary: #30363D;
            --border-secondary: #21262D;
            --success: #28A745;
            --danger: #F59E0B;

            /* 字体大小变量 - 弹性计算 */
            --base-font-size: 1rem;
            --small-font-size: 0.85rem;
            --medium-font-size: 1.1rem;
            --large-font-size: 1.3rem;
            --xlarge-font-size: 1.6rem;
            --xxlarge-font-size: 2rem;

            /* 间距变量 */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 0.75rem;
            --spacing-lg: 1rem;
            --spacing-xl: 1.5rem;
        }
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body, html {
            font-family: 'Noto Sans SC', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: var(--base-font-size);
            line-height: 1.4;
            -webkit-font-smoothing: antialiased;
            overflow: hidden;
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
        }
        .report-container {
            width: 1280px;
            height: 720px;
            background: var(--bg-primary);
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            position: relative;
        }
        .report-header {
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-primary);
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
        }
        .header-title h1 {
            font-family: 'Noto Sans SC', sans-serif;
            font-size: var(--xlarge-font-size);
            font-weight: 700;
        }
        .header-title h1 .logo {
            font-family: 'JetBrains Mono', monospace;
            margin-right: 6px;
            color: var(--accent-primary);
        }
        .header-meta {
            background-color: #0D1117;
            border-radius: 12px;
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
        }
        .header-meta strong {
            color: var(--text-secondary);
        }
        .report-main {
            flex-grow: 1;
            padding: 10px;
            display: flex;
            gap: 10px;
            overflow: hidden;
            height: calc(720px - 50px - 25px);
        }
        .left-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .center-column {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .right-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
            height: 100%;
        }
        .module-card {
            background: var(--bg-module);
            border-radius: 6px;
            border: 1px solid var(--border-primary);
            padding: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .module-card .module-title {
            font-size: var(--medium-font-size);
            font-weight: 500;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            flex-shrink: 0;
        }
        .module-card.stretchy {
            flex-grow: 1;
            min-height: 0;
        }
        .analysis-list {
            list-style: none;
            font-size: var(--base-font-size);
            padding-left: var(--spacing-xs);
            overflow: hidden;
            flex-grow: 1;
        }
        .analysis-list li {
            margin-bottom: var(--spacing-sm);
            line-height: 1.3;
        }
        .enhanced-metrics-table {
            width: 100%;
            border-collapse: collapse;
            flex-grow: 1;
        }
        .enhanced-metrics-table td {
            padding: var(--spacing-sm) var(--spacing-xs);
            border-bottom: 1px solid var(--border-secondary);
            font-size: var(--base-font-size);
            vertical-align: middle;
        }
        .enhanced-metrics-table tr:last-child td {
            border-bottom: none;
            padding-bottom: 0;
        }
        .enhanced-metrics-table tr:first-child td {
            padding-top: 0;
        }
        .enhanced-metrics-table .metric-label {
            color: var(--text-muted);
            line-height: 1.2;
        }
        .enhanced-metrics-table .metric-value {
            font-weight: 500;
            text-align: right;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }
        .core-metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-bottom: 0;
        }
        .metric-item {
            background-color: var(--bg-secondary);
            border-radius: 4px;
            padding: var(--spacing-md);
            text-align: center;
            border: 1px solid var(--border-secondary);
        }
        .metric-item .label {
            font-size: var(--small-font-size);
            color: var(--text-muted);
            margin-bottom: var(--spacing-xs);
            display: block;
        }
        .metric-item .value {
            font-size: var(--large-font-size);
            font-weight: 700;
            font-family: 'JetBrains Mono', monospace;
        }
        .chart-module {
            flex-grow: 1;
            padding: 12px;
            min-height: 0;
        }
        .chart-container {
            flex-grow: 1;
            min-height: 0;
            height: 100%;
        }
        #chart_container {
            width: 100%;
            height: 100%;
        }
        .table-module {
            flex-grow: 1;
            min-height: 0;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .table-container {
            flex-grow: 1;
            max-height: 100%;
            min-height: 0;
            display: flex;
            flex-direction: column;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--small-font-size);
            flex-grow: 1;
            height: 100%;
            table-layout: fixed;
        }
        .data-table th {
            background-color: var(--bg-secondary);
            color: var(--text-muted);
            font-weight: 500;
            padding: var(--spacing-sm);
            text-align: center;
            border-bottom: 1px solid var(--border-primary);
            position: sticky;
            top: 0;
            z-index: 1;
            white-space: nowrap;
        }
        .data-table td {
            padding: var(--spacing-sm);
            border-bottom: 1px solid var(--border-secondary);
            vertical-align: middle;
            text-align: center;
            word-wrap: break-word;
        }
        .data-table tr:hover {
            background-color: var(--bg-secondary);
        }
        .data-table tbody {
            height: 100%;
        }

        /* 主要交易对表格列宽 - 调整宽度避免截断 */
        .data-table.pairs-table {
            table-layout: fixed;
        }
        .data-table.pairs-table th:nth-child(1),
        .data-table.pairs-table td:nth-child(1) {
            width: 35%;
            text-align: left;
        }
        .data-table.pairs-table th:nth-child(2),
        .data-table.pairs-table td:nth-child(2) {
            width: 25%;
            text-align: right;
        }
        .data-table.pairs-table th:nth-child(3),
        .data-table.pairs-table td:nth-child(3) {
            width: 20%;
            text-align: right;
        }
        .data-table.pairs-table th:nth-child(4),
        .data-table.pairs-table td:nth-child(4) {
            width: 20%;
            text-align: right;
        }

        /* 流动性分布表格列宽 - 流动性和占比往左移 */
        .data-table.liquidity-table {
            table-layout: fixed;
        }
        .data-table.liquidity-table th:nth-child(1),
        .data-table.liquidity-table td:nth-child(1) {
            width: 50%;
            text-align: left;
        }
        .data-table.liquidity-table th:nth-child(2),
        .data-table.liquidity-table td:nth-child(2) {
            width: 30%;
            text-align: right !important;
        }
        .data-table.liquidity-table th:nth-child(3),
        .data-table.liquidity-table td:nth-child(3) {
            width: 20%;
            text-align: right !important;
        }

        /* 确保流动性表格数据与表头对齐 */
        .data-table.liquidity-table .number-cell {
            text-align: right !important;
        }
        .data-table.liquidity-table .percent-cell {
            text-align: right !important;
        }

        /* 交易对列特殊样式 */
        .data-table .pair-cell {
            white-space: normal;
            line-height: 1.2;
            font-size: var(--small-font-size);
            text-align: left !important;
            word-wrap: break-word;
            overflow: visible;
        }
        .data-table .pair-name {
            font-weight: 600;
            color: var(--text-primary);
            display: block;
        }
        .data-table .pair-dex {
            color: var(--text-muted);
            font-size: calc(var(--small-font-size) - 1px);
            display: block;
            margin-top: 2px;
        }
        /* 数值列样式 */
        .data-table .number-cell {
            text-align: right !important;
            font-family: 'JetBrains Mono', monospace;
            font-weight: 500;
            white-space: nowrap;
            overflow: visible;
        }
        /* 百分比列样式 */
        .data-table .percent-cell {
            text-align: right !important;
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
            white-space: nowrap;
            overflow: visible;
        }
        /* 响应式调整 */
        @media (max-width: 1200px) {
            .data-table {
                font-size: calc(var(--small-font-size) - 1px);
            }
            .data-table th, .data-table td {
                padding: calc(var(--spacing-sm) - 2px);
            }
        }
        .footer {
            padding: 8px 20px;
            background: var(--bg-secondary);
            border-top: 1px solid var(--border-primary);
            text-align: center;
            font-size: var(--small-font-size);
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
            flex-shrink: 0;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <div class="header-title">
                <h1><span class="logo">🐭 PYTHIA</span>分析报告</h1>
            </div>
            <div class="header-meta">
                <span><strong>Token:</strong> CreiuhfwdWCN...</span> |
                <span><strong>Chain:</strong> Solana</span> |
                <span><strong>Time:</strong> 2025-07-27 05:35</span>
            </div>
        </header>
        <main class="report-main">
            <!-- LEFT COLUMN -->
            <div class="left-column">
                <div class="module-card">
                    <h2 class="module-title">✅ 积极因素</h2>
                    <ul class="analysis-list"><li>高交易活跃度: 日交易量$7M</li></ul>
                </div>
                <div class="module-card stretchy">
                    <h2 class="module-title">🔬 增强指标</h2>
                    <table class="enhanced-metrics-table">
                        <tbody>
                            <tr>
                                <td class="metric-label">平均价格变化</td>
                                <td class="metric-value">4.40%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">价格一致性</td>
                                <td class="metric-value">0.24%</td>
                            </tr>
                            <tr>
                                <td class="metric-label">交易活跃度</td>
                                <td class="metric-value">12582 笔/对</td>
                            </tr>
                            <tr>
                                <td class="metric-label">市场深度</td>
                                <td class="metric-value">1.952</td>
                            </tr>
                            <tr>
                                <td class="metric-label">情绪指数</td>
                                <td class="metric-value">😐 中性</td>
                            </tr>
                            <tr>
                                <td class="metric-label">买卖比</td>
                                <td class="metric-value">1.02:1</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- CENTER COLUMN -->
            <div class="center-column">
                <div class="module-card">
                    <div class="core-metrics-grid">
                        <div class="metric-item">
                            <div class="label">市值</div>
                            <div class="value">$97.4M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">24h交易量</div>
                            <div class="value">$6.8M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">流动性</div>
                            <div class="value">$13.3M</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">换手率</div>
                            <div class="value">7.0%</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">交易对数</div>
                            <div class="value">2</div>
                        </div>
                        <div class="metric-item">
                            <div class="label">总交易笔数</div>
                            <div class="value">25,165</div>
                        </div>
                    </div>
                </div>
                <div class="module-card stretchy chart-module">
                    <h2 class="module-title">📈 价格走势图 (24H)</h2>
                    <div class="chart-container">
                        <div id="chart_container"></div>
                    </div>
                </div>
            </div>
            <!-- RIGHT COLUMN -->
            <div class="right-column">
                <div class="module-card table-module">
                    <h2 class="module-title">💰 主要交易对</h2>
                    <div class="table-container">
                        <table class="data-table pairs-table">
                            <thead>
                                <tr><th>交易对</th><th>价格</th><th>24h%</th><th>交易量</th></tr>
                            </thead>
                            <tbody>
                <tr>
                    <td class="pair-cell">
                        <span class="pair-name">PYTHIA/SOL</span>
                        <span class="pair-dex">meteora</span>
                    </td>
                    <td class="number-cell">$0.09743</td>
                    <td class="percent-cell" style="color: #28A745;">*****%</td>
                    <td class="number-cell">$6.66M</td>
                </tr>
                <tr>
                    <td class="pair-cell">
                        <span class="pair-name">PYTHIA/SOL</span>
                        <span class="pair-dex">raydium</span>
                    </td>
                    <td class="number-cell">$0.09766</td>
                    <td class="percent-cell" style="color: #28A745;">*****%</td>
                    <td class="number-cell">$153.3K</td>
                </tr></tbody>
                        </table>
                    </div>
                </div>
                <div class="module-card table-module">
                    <h2 class="module-title">💧 流动性分布</h2>
                    <div class="table-container">
                        <table class="data-table liquidity-table">
                            <thead>
                                <tr><th>交易对</th><th>流动性</th><th>占比</th></tr>
                            </thead>
                            <tbody>
                    <tr>
                        <td class="pair-cell">
                            <span class="pair-name">PYTHIA/SOL</span>
                            <span class="pair-dex">meteora</span>
                        </td>
                        <td class="number-cell">$10.19M</td>
                        <td class="percent-cell">76.6%</td>
                    </tr>
                    <tr>
                        <td class="pair-cell">
                            <span class="pair-name">PYTHIA/SOL</span>
                            <span class="pair-dex">raydium</span>
                        </td>
                        <td class="number-cell">$3.11M</td>
                        <td class="percent-cell">23.4%</td>
                    </tr></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
        <footer class="footer">
            PYTHIA AI 增强分析工具 v3.0 | "数据驱动决策，理性投资未来"
        </footer>
    </div>
    <!-- TradingView Widget Script - 专业版本 -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
        // 确保TradingView全局变量可用，兼容html_to_image_headless.py
        window.TradingView = window.TradingView || {};

        // 图表加载状态标记
        window.chartLoaded = false;
        window.tradingViewLoaded = false;

        function initializeTradingViewChart() {
            try {
                // 检查容器是否存在
                const container = document.getElementById('chart_container');
                if (!container) {
                    console.error('Chart container not found');
                    return;
                }

                // 标记TradingView已加载
                window.tradingViewLoaded = true;

                // 创建TradingView图表 - 使用专业配置
                const widget = new TradingView.widget({
                    "autosize": true,
                    "symbol": "CRYPTO:PYTHIAUSD",
                    "interval": "30",
                    "theme": "dark",
                    "style": "1",
                    "locale": "zh_CN",
                    "toolbar_bg": "#161B22",
                    "enable_publishing": false,
                    "hide_top_toolbar": true,
                    "hide_side_toolbar": true,
                    "hide_legend": true,
                    "container_id": "chart_container",
                    "overrides": {
                        "paneProperties.background": "#161B22",
                        "paneProperties.vertGridProperties.color": "rgba(255,255,255,0.05)",
                        "paneProperties.horzGridProperties.color": "rgba(255,255,255,0.05)",
                        "mainSeriesProperties.candleStyle.upColor": "#28A745",
                        "mainSeriesProperties.candleStyle.downColor": "#DC3545",
                        "mainSeriesProperties.candleStyle.borderUpColor": "#28A745",
                        "mainSeriesProperties.candleStyle.borderDownColor": "#DC3545",
                        "scalesProperties.textColor": "#7D8590"
                    },
                    "onChartReady": function() {
                        // 图表准备就绪
                        window.chartLoaded = true;
                        console.log('TradingView chart loaded successfully');
                    }
                });

                // 备用标记，防止onChartReady不触发
                setTimeout(function() {
                    window.chartLoaded = true;
                }, 5000);

            } catch (error) {
                console.error('TradingView chart initialization error:', error);
                // 即使出错也标记为已加载，避免无限等待
                window.chartLoaded = true;
                window.tradingViewLoaded = true;
            }
        }

        // 页面加载完成后初始化图表
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeTradingViewChart);
        } else {
            initializeTradingViewChart();
        }

        // 窗口加载完成后的备用初始化
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (!window.tradingViewLoaded) {
                    initializeTradingViewChart();
                }
                // 最终备用标记
                setTimeout(function() {
                    window.chartLoaded = true;
                    window.tradingViewLoaded = true;
                }, 3000);
            }, 1000);
        });
    </script>
</body>
</html>