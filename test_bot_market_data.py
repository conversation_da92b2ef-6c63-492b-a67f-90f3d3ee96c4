#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Bot加载市场数据功能
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_market_data_loading():
    """测试市场数据加载功能"""
    print("🤖 测试Bot市场数据加载功能")
    print("=" * 60)
    
    try:
        # 导入bot模块
        from bot import AIBot

        # 创建bot实例
        bot = AIBot()
        print("✅ Bot实例创建成功")
        
        # 测试市场数据加载
        print("\n📊 测试市场数据加载...")
        market_data = bot._get_latest_market_data()
        
        print(f"📄 市场数据长度: {len(market_data)} 字符")
        print(f"📄 市场数据行数: {market_data.count(chr(10))} 行")
        
        # 显示市场数据前几行
        print("\n📋 市场数据预览:")
        lines = market_data.split('\n')
        for i, line in enumerate(lines[:15], 1):
            print(f"   {i:2d}: {line}")
        
        if len(lines) > 15:
            print(f"   ... (还有 {len(lines)-15} 行)")
        
        # 测试系统提示词生成
        print("\n🧠 测试系统提示词生成...")
        system_prompt = bot._get_chinese_system_prompt()
        
        print(f"📄 系统提示词长度: {len(system_prompt)} 字符")
        print(f"📄 系统提示词行数: {system_prompt.count(chr(10))} 行")
        
        # 检查是否包含市场数据
        has_market_data = "MARKET_SUMMARY" in system_prompt or "total_pairs" in system_prompt
        print(f"🔍 是否包含市场数据: {'✅ 是' if has_market_data else '❌ 否'}")
        
        # 检查关键部分
        key_sections = {
            "时间戳": "TIMESTAMP" in system_prompt,
            "市场摘要": "MARKET_SUMMARY" in system_prompt,
            "交易对数据": "TRADING_PAIRS" in system_prompt,
            "价格范围": "PRICE_RANGE" in system_prompt,
            "DEX分布": "DEX_DISTRIBUTION" in system_prompt
        }
        
        print(f"\n🔍 系统提示词内容检查:")
        for section, exists in key_sections.items():
            status = "✅" if exists else "❌"
            print(f"   {status} {section}")
        
        # 显示系统提示词的市场数据部分
        if has_market_data:
            print(f"\n📊 系统提示词中的市场数据部分:")
            lines = system_prompt.split('\n')
            in_market_section = False
            market_lines = []
            
            for line in lines:
                if "最新市场数据" in line or "TIMESTAMP:" in line:
                    in_market_section = True
                elif in_market_section and line.strip() == "":
                    # 遇到空行，检查是否结束市场数据部分
                    if market_lines and not any(keyword in line for keyword in ["total_pairs", "market_cap", "volume", "pair_"]):
                        break
                
                if in_market_section:
                    market_lines.append(line)
                    if len(market_lines) >= 20:  # 只显示前20行
                        break
            
            for i, line in enumerate(market_lines[:20], 1):
                print(f"   {i:2d}: {line}")
            
            if len(market_lines) > 20:
                print(f"   ... (还有 {len(market_lines)-20} 行)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_txt_folder():
    """测试data_txt文件夹状态"""
    print("\n📁 检查data_txt文件夹状态...")
    
    data_txt_dir = "data_txt"
    if not os.path.exists(data_txt_dir):
        print("❌ data_txt文件夹不存在")
        return False
    
    txt_files = [f for f in os.listdir(data_txt_dir) if f.endswith('.txt')]
    if not txt_files:
        print("❌ data_txt文件夹中没有txt文件")
        return False
    
    print(f"✅ 找到 {len(txt_files)} 个txt文件:")
    for i, file in enumerate(sorted(txt_files), 1):
        filepath = os.path.join(data_txt_dir, file)
        file_size = os.path.getsize(filepath)
        mod_time = datetime.fromtimestamp(os.path.getmtime(filepath))
        print(f"   {i}. {file} ({file_size} 字节, {mod_time.strftime('%Y-%m-%d %H:%M:%S')})")
    
    # 显示最新文件内容
    latest_file = max(txt_files, key=lambda f: os.path.getctime(os.path.join(data_txt_dir, f)))
    print(f"\n📄 最新文件内容预览 ({latest_file}):")
    
    with open(os.path.join(data_txt_dir, latest_file), 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    for i, line in enumerate(lines[:10], 1):
        print(f"   {i:2d}: {line}")
    
    if len(lines) > 10:
        print(f"   ... (还有 {len(lines)-10} 行)")
    
    return True

if __name__ == "__main__":
    print(f"🕒 测试开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试data_txt文件夹
    folder_ok = test_data_txt_folder()
    
    if folder_ok:
        # 测试bot市场数据加载
        success = test_market_data_loading()
        
        print("\n" + "=" * 60)
        print("📋 测试结果总结:")
        print(f"   data_txt文件夹: {'✅ 正常' if folder_ok else '❌ 异常'}")
        print(f"   Bot数据加载: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print("\n🎉 Bot已成功集成市场数据！")
            print("💡 现在Bot可以基于最新的PYTHIA市场数据回答用户问题")
            print("🤖 系统提示词已包含实时市场信息")
        else:
            print("\n❌ Bot数据集成失败，请检查配置")
    else:
        print("\n❌ 请先运行PYTHIA分析程序生成市场数据")
    
    print(f"\n🕒 测试结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
