# Bot格式限制提醒

## 🚫 严格禁止的格式

### Markdown格式
- **粗体** 和 *斜体*
- `代码` 和 ```代码块```
- # 标题 和 ## 子标题
- [链接](url) 格式
- > 引用格式
- - 列表 和 * 列表 和 1. 编号列表

### HTML格式
- <b>粗体</b> 和 <i>斜体</i>
- <code>代码</code>
- 任何HTML标签

## ✅ 允许使用的格式

- 普通文字
- emoji表情符号 🐭 📊 💡
- 标点符号：。，！？：；""''
- 数字和字母
- 空格和换行

## 🎯 目标效果

让Bot的回答就像普通的微信聊天一样，纯文本对话，自然流畅。

## 📝 示例对比

❌ 错误：**PYTHIA代币**的*市值*是`$92,588,346`
✅ 正确：PYTHIA代币的市值是92,588,346美元

❌ 错误：
- 交易量：622万美元
- 流动性：1275万美元

✅ 正确：交易量是622万美元，流动性有1275万美元

## 🔧 技术实现

在bot.py的系统提示词中已经添加了严格的格式限制规则，确保AI不会使用任何格式化语法。
